'use client'

import { useState, useEffect } from 'react'
import { Pa<PERSON>, Wand2, Download, Refresh<PERSON><PERSON>, BookO<PERSON>, Sparkles } from 'lucide-react'

interface ComicGeneratorProps {
  dreamData: any
  onComicGenerated: (comic: any) => void
}

export default function ComicGenerator({ dreamData, onComicGenerated }: ComicGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [generationProgress, setGenerationProgress] = useState(0)
  const [selectedStyle, setSelectedStyle] = useState('manga')
  const [comic, setComic] = useState<any>(null)

  const styles = [
    { id: 'manga', name: 'Manga', description: 'Estilo japonés clásico', color: 'from-pink-500 to-red-500' },
    { id: 'comic', name: 'Cómic', description: 'Estilo americano vibrante', color: 'from-blue-500 to-purple-500' },
    { id: 'watercolor', name: '<PERSON><PERSON><PERSON><PERSON>', description: 'Suave y artístico', color: 'from-green-500 to-blue-500' },
    { id: 'noir', name: 'Noir', description: 'Blanco y negro dramático', color: 'from-gray-700 to-black' }
  ]

  const generateComic = async () => {
    if (!dreamData) return

    setIsGenerating(true)
    setGenerationProgress(0)

    // Simular progreso de generación
    const steps = [
      'Analizando narrativa...',
      'Creando personajes...',
      'Diseñando escenarios...',
      'Generando viñetas...',
      'Aplicando estilo...',
      'Finalizando cómic...'
    ]

    for (let i = 0; i < steps.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 1000))
      setGenerationProgress(((i + 1) / steps.length) * 100)
    }

    // Generar cómic simulado
    const mockComic = {
      id: Date.now(),
      title: 'El Viaje de los Sueños',
      style: selectedStyle,
      panels: [
        {
          id: 1,
          description: 'Protagonista caminando por un bosque misterioso',
          dialogue: '¿Dónde estoy? Este lugar me resulta familiar...',
          image: '/api/placeholder/300/200'
        },
        {
          id: 2,
          description: 'Encuentro con el animal guía',
          dialogue: 'Sígueme, te mostraré el camino a casa.',
          image: '/api/placeholder/300/200'
        },
        {
          id: 3,
          description: 'Casa flotando en las nubes',
          dialogue: '¡Mi casa de la infancia! Pero... ¿cómo puede estar aquí?',
          image: '/api/placeholder/300/200'
        },
        {
          id: 4,
          description: 'Figura familiar esperando',
          dialogue: 'Has crecido mucho. Es hora de que sepas la verdad.',
          image: '/api/placeholder/300/200'
        }
      ],
      createdAt: new Date().toISOString(),
      dreamData: dreamData
    }

    setComic(mockComic)
    onComicGenerated(mockComic)
    setIsGenerating(false)

    // Guardar en localStorage para la galería
    const savedComics = JSON.parse(localStorage.getItem('dreamComics') || '[]')
    savedComics.push(mockComic)
    localStorage.setItem('dreamComics', JSON.stringify(savedComics))
  }

  if (!dreamData) {
    return (
      <div className="dream-card p-8 max-w-4xl mx-auto text-center">
        <Palette className="w-16 h-16 text-gray-500 mx-auto mb-4 opacity-50" />
        <h2 className="text-2xl font-bold text-white mb-2">Generador de Cómics</h2>
        <p className="text-gray-400">
          Primero necesitas analizar un sueño para generar tu historieta
        </p>
      </div>
    )
  }

  return (
    <div className="dream-card p-8 max-w-6xl mx-auto">
      <div className="text-center mb-8">
        <Palette className="w-16 h-16 text-purple-300 mx-auto mb-4 animate-pulse" />
        <h2 className="text-3xl font-bold text-white mb-2">Generador de Cómics</h2>
        <p className="text-purple-200">
          Convierte tu sueño en una historieta personalizada
        </p>
      </div>

      {!comic ? (
        <div className="space-y-8">
          {/* Selector de Estilo */}
          <div>
            <h3 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
              <Wand2 className="w-6 h-6" />
              Elige tu Estilo
            </h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
              {styles.map((style) => (
                <button
                  key={style.id}
                  onClick={() => setSelectedStyle(style.id)}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    selectedStyle === style.id
                      ? 'border-purple-400 bg-purple-600/20'
                      : 'border-white/20 bg-white/5 hover:border-white/40'
                  }`}
                >
                  <div className={`w-full h-20 rounded-lg bg-gradient-to-br ${style.color} mb-3`} />
                  <h4 className="text-white font-bold">{style.name}</h4>
                  <p className="text-gray-300 text-sm">{style.description}</p>
                </button>
              ))}
            </div>
          </div>

          {/* Vista Previa del Sueño */}
          <div className="bg-white/5 rounded-lg p-6">
            <h3 className="text-lg font-bold text-white mb-4">Vista Previa del Sueño</h3>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-white font-medium mb-2">Personajes:</h4>
                <div className="flex flex-wrap gap-2">
                  {dreamData.characters?.map((character: string, index: number) => (
                    <span key={index} className="bg-blue-600/30 text-blue-200 px-2 py-1 rounded text-sm">
                      {character}
                    </span>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="text-white font-medium mb-2">Escenarios:</h4>
                <div className="flex flex-wrap gap-2">
                  {dreamData.settings?.map((setting: string, index: number) => (
                    <span key={index} className="bg-green-600/30 text-green-200 px-2 py-1 rounded text-sm">
                      {setting}
                    </span>
                  ))}
                </div>
              </div>
            </div>
            <div className="mt-4">
              <h4 className="text-white font-medium mb-2">Narrativa:</h4>
              <p className="text-gray-300 text-sm leading-relaxed">{dreamData.narrative}</p>
            </div>
          </div>

          {/* Botón de Generación */}
          <div className="text-center">
            <button
              onClick={generateComic}
              disabled={isGenerating}
              className={`py-4 px-8 rounded-lg font-bold text-lg transition-all ${
                isGenerating 
                  ? 'bg-gray-600 text-gray-300 cursor-not-allowed'
                  : 'bg-purple-600 hover:bg-purple-700 text-white'
              }`}
            >
              {isGenerating ? (
                <div className="flex items-center gap-2">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Generando Cómic...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Sparkles className="w-5 h-5" />
                  Generar Historieta
                </div>
              )}
            </button>

            {isGenerating && (
              <div className="mt-6 max-w-md mx-auto">
                <div className="bg-white/10 rounded-full h-3 mb-4">
                  <div 
                    className="bg-gradient-to-r from-purple-500 to-pink-500 h-3 rounded-full transition-all duration-300"
                    style={{ width: `${generationProgress}%` }}
                  />
                </div>
                <p className="text-purple-200">Creando tu historieta... {Math.round(generationProgress)}%</p>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="space-y-8">
          {/* Header del Cómic */}
          <div className="text-center">
            <h3 className="text-2xl font-bold text-white mb-2">{comic.title}</h3>
            <p className="text-purple-200">Estilo: {styles.find(s => s.id === comic.style)?.name}</p>
          </div>

          {/* Paneles del Cómic */}
          <div className="grid md:grid-cols-2 gap-6">
            {comic.panels.map((panel: any) => (
              <div key={panel.id} className="comic-panel">
                <div className="bg-gray-200 h-48 rounded-lg mb-4 flex items-center justify-center">
                  <div className="text-center text-gray-600">
                    <BookOpen className="w-12 h-12 mx-auto mb-2" />
                    <p className="text-sm">{panel.description}</p>
                  </div>
                </div>
                <div className="bg-yellow-100 p-3 rounded-lg border-2 border-yellow-400">
                  <p className="text-gray-800 font-medium italic">"{panel.dialogue}"</p>
                </div>
              </div>
            ))}
          </div>

          {/* Acciones */}
          <div className="flex justify-center gap-4">
            <button
              onClick={() => setComic(null)}
              className="flex items-center gap-2 py-3 px-6 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-all"
            >
              <RefreshCw className="w-5 h-5" />
              Generar Nuevo
            </button>
            
            <button
              className="flex items-center gap-2 py-3 px-6 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-all"
            >
              <Download className="w-5 h-5" />
              Descargar
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
