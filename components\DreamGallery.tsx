'use client'

import { useState, useEffect } from 'react'
import { BookOpen, Calendar, Palette, Eye, Trash2, Star, Filter } from 'lucide-react'

export default function DreamGallery() {
  const [comics, setComics] = useState<any[]>([])
  const [selectedComic, setSelectedComic] = useState<any>(null)
  const [filterStyle, setFilterStyle] = useState('all')

  useEffect(() => {
    // Cargar cómics guardados
    const savedComics = JSON.parse(localStorage.getItem('dreamComics') || '[]')
    setComics(savedComics)
  }, [])

  const deleteComic = (comicId: number) => {
    const updatedComics = comics.filter(comic => comic.id !== comicId)
    setComics(updatedComics)
    localStorage.setItem('dreamComics', JSON.stringify(updatedComics))
    if (selectedComic?.id === comicId) {
      setSelectedComic(null)
    }
  }

  const filteredComics = filterStyle === 'all' 
    ? comics 
    : comics.filter(comic => comic.style === filterStyle)

  const styles = ['all', 'manga', 'comic', 'watercolor', 'noir']
  const styleNames: { [key: string]: string } = {
    all: 'Todos',
    manga: 'Manga',
    comic: 'Cómic',
    watercolor: 'Acuarela',
    noir: 'Noir'
  }

  return (
    <div className="dream-card p-8 max-w-6xl mx-auto">
      <div className="text-center mb-8">
        <BookOpen className="w-16 h-16 text-green-300 mx-auto mb-4 animate-pulse" />
        <h2 className="text-3xl font-bold text-white mb-2">Galería de Sueños</h2>
        <p className="text-green-200">
          Explora tu colección personal de historietas oníricas
        </p>
      </div>

      {comics.length === 0 ? (
        <div className="text-center py-12">
          <BookOpen className="w-24 h-24 text-gray-500 mx-auto mb-4 opacity-50" />
          <h3 className="text-xl font-bold text-gray-400 mb-2">No hay historietas aún</h3>
          <p className="text-gray-500">
            Genera tu primera historieta analizando un sueño y creando un cómic
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Filtros */}
          <div className="flex items-center gap-4 mb-6">
            <Filter className="w-5 h-5 text-white" />
            <div className="flex gap-2">
              {styles.map((style) => (
                <button
                  key={style}
                  onClick={() => setFilterStyle(style)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                    filterStyle === style
                      ? 'bg-green-600 text-white'
                      : 'bg-white/10 text-gray-300 hover:bg-white/20'
                  }`}
                >
                  {styleNames[style]}
                </button>
              ))}
            </div>
          </div>

          {selectedComic ? (
            /* Vista Detallada del Cómic */
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <button
                  onClick={() => setSelectedComic(null)}
                  className="text-blue-400 hover:text-blue-300 flex items-center gap-2"
                >
                  ← Volver a la galería
                </button>
                <div className="flex items-center gap-2 text-gray-300">
                  <Calendar className="w-4 h-4" />
                  {new Date(selectedComic.createdAt).toLocaleDateString()}
                </div>
              </div>

              <div className="text-center">
                <h3 className="text-2xl font-bold text-white mb-2">{selectedComic.title}</h3>
                <div className="flex items-center justify-center gap-4 text-sm text-gray-300">
                  <span className="flex items-center gap-1">
                    <Palette className="w-4 h-4" />
                    {styleNames[selectedComic.style]}
                  </span>
                  <span>{selectedComic.panels.length} paneles</span>
                </div>
              </div>

              {/* Información del Sueño Original */}
              <div className="bg-white/5 rounded-lg p-6">
                <h4 className="text-lg font-bold text-white mb-4">Datos del Sueño Original</h4>
                <div className="grid md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Duración:</span>
                    <div className="text-white font-medium">{selectedComic.dreamData.duration}</div>
                  </div>
                  <div>
                    <span className="text-gray-400">Fases REM:</span>
                    <div className="text-white font-medium">{selectedComic.dreamData.remPhases}</div>
                  </div>
                  <div>
                    <span className="text-gray-400">Intensidad:</span>
                    <div className="text-white font-medium">{selectedComic.dreamData.intensity}/10</div>
                  </div>
                </div>
                <div className="mt-4">
                  <span className="text-gray-400">Emociones:</span>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {selectedComic.dreamData.emotions.map((emotion: string, index: number) => (
                      <span key={index} className="bg-pink-600/30 text-pink-200 px-2 py-1 rounded text-xs">
                        {emotion}
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              {/* Paneles del Cómic */}
              <div className="grid md:grid-cols-2 gap-6">
                {selectedComic.panels.map((panel: any) => (
                  <div key={panel.id} className="comic-panel">
                    <div className="bg-gray-200 h-48 rounded-lg mb-4 flex items-center justify-center">
                      <div className="text-center text-gray-600">
                        <BookOpen className="w-12 h-12 mx-auto mb-2" />
                        <p className="text-sm px-4">{panel.description}</p>
                      </div>
                    </div>
                    <div className="bg-yellow-100 p-3 rounded-lg border-2 border-yellow-400">
                      <p className="text-gray-800 font-medium italic">"{panel.dialogue}"</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            /* Vista de Galería */
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredComics.map((comic) => (
                <div key={comic.id} className="bg-white/5 rounded-lg p-6 hover:bg-white/10 transition-all cursor-pointer group">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-bold text-white mb-1">{comic.title}</h3>
                      <div className="flex items-center gap-2 text-sm text-gray-300">
                        <Palette className="w-4 h-4" />
                        {styleNames[comic.style]}
                      </div>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        deleteComic(comic.id)
                      }}
                      className="text-red-400 hover:text-red-300 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>

                  {/* Preview del primer panel */}
                  <div className="bg-gray-200 h-32 rounded-lg mb-4 flex items-center justify-center">
                    <div className="text-center text-gray-600">
                      <BookOpen className="w-8 h-8 mx-auto mb-1" />
                      <p className="text-xs px-2">{comic.panels[0]?.description}</p>
                    </div>
                  </div>

                  {/* Metadata */}
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center justify-between text-gray-300">
                      <span>{comic.panels.length} paneles</span>
                      <span className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        {new Date(comic.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                    
                    <div className="flex flex-wrap gap-1">
                      {comic.dreamData.emotions.slice(0, 3).map((emotion: string, index: number) => (
                        <span key={index} className="bg-purple-600/30 text-purple-200 px-2 py-1 rounded text-xs">
                          {emotion}
                        </span>
                      ))}
                    </div>
                  </div>

                  <button
                    onClick={() => setSelectedComic(comic)}
                    className="w-full mt-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-all flex items-center justify-center gap-2"
                  >
                    <Eye className="w-4 h-4" />
                    Ver Completo
                  </button>
                </div>
              ))}
            </div>
          )}

          {filteredComics.length === 0 && filterStyle !== 'all' && (
            <div className="text-center py-8">
              <p className="text-gray-400">
                No hay historietas con el estilo "{styleNames[filterStyle]}"
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
