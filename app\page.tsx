'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Palette } from 'lucide-react'
import EEGConnector from '@/components/EEGConnector'
import DreamAnalyzer from '@/components/DreamAnalyzer'
import ComicGenerator from '@/components/ComicGenerator'
import DreamGallery from '@/components/DreamGallery'

export default function Home() {
  const [currentStep, setCurrentStep] = useState(0)
  const [eegConnected, setEegConnected] = useState(false)
  const [dreamData, setDreamData] = useState(null)
  const [generatedComic, setGeneratedComic] = useState(null)

  const steps = [
    { icon: Brain, title: 'Conectar EEG', description: 'Conecta tu banda cerebral' },
    { icon: Moon, title: '<PERSON><PERSON><PERSON>', description: 'Procesa las ondas cerebrales' },
    { icon: Palette, title: 'Generar Cómic', description: 'Crea tu historieta' },
    { icon: BookO<PERSON>, title: '<PERSON><PERSON><PERSON>', description: 'Explora tus sueños' }
  ]

  return (
    <div className="min-h-screen p-4">
      {/* Header */}
      <header className="text-center mb-8">
        <div className="flex items-center justify-center gap-3 mb-4">
          <Brain className="w-12 h-12 text-purple-300 animate-pulse" />
          <h1 className="text-5xl font-bold text-white">
            Som<span className="text-purple-300">ní</span>grafo
          </h1>
          <Sparkles className="w-12 h-12 text-yellow-300 animate-bounce" />
        </div>
        <p className="text-xl text-purple-200 max-w-2xl mx-auto">
          El traductor de sueños en historietas. Conecta tu banda cerebral y convierte tus sueños en cómics personalizados.
        </p>
      </header>

      {/* Navigation Steps */}
      <div className="flex justify-center mb-8">
        <div className="flex space-x-4">
          {steps.map((step, index) => {
            const Icon = step.icon
            return (
              <button
                key={index}
                onClick={() => setCurrentStep(index)}
                className={`flex flex-col items-center p-4 rounded-lg transition-all ${
                  currentStep === index
                    ? 'bg-white/20 text-white scale-105'
                    : 'bg-white/5 text-purple-200 hover:bg-white/10'
                }`}
              >
                <Icon className="w-8 h-8 mb-2" />
                <span className="text-sm font-medium">{step.title}</span>
              </button>
            )
          })}
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto">
        {currentStep === 0 && (
          <EEGConnector 
            onConnect={setEegConnected}
            isConnected={eegConnected}
          />
        )}
        
        {currentStep === 1 && (
          <DreamAnalyzer 
            isConnected={eegConnected}
            onDreamAnalyzed={setDreamData}
          />
        )}
        
        {currentStep === 2 && (
          <ComicGenerator 
            dreamData={dreamData}
            onComicGenerated={setGeneratedComic}
          />
        )}
        
        {currentStep === 3 && (
          <DreamGallery />
        )}
      </div>

      {/* Status Bar */}
      <div className="fixed bottom-4 right-4">
        <div className="dream-card p-4">
          <div className="flex items-center gap-2">
            <Zap className={`w-5 h-5 ${eegConnected ? 'text-green-400' : 'text-gray-400'}`} />
            <span className="text-white text-sm">
              {eegConnected ? 'EEG Conectado' : 'EEG Desconectado'}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
