# Somnígrafo - El Traductor de Sueños en Historietas

Una aplicación innovadora que conecta con bandas cerebrales EEG para capturar ondas cerebrales durante el sueño y generar historietas personalizadas basadas en los sueños.

## Características

- 🧠 **Conexión EEG**: Simula la conexión con dispositivos de electroencefalografía
- 🌙 **Análisis de Sueños**: Procesa las ondas cerebrales para interpretar los sueños
- 🎨 **Generación de Cómics**: Crea historietas en diferentes estilos (Manga, Cómic, Acuarela, Noir)
- 📚 **Galería Personal**: Almacena y organiza tu colección de sueños visualizados

## Tecnologías

- **Frontend**: Next.js 14, React 18, TypeScript
- **Estilos**: Tailwind CSS
- **Iconos**: Lucide React
- **Animaciones**: Framer Motion

## Instalación

```bash
npm install
npm run dev
```

## Uso

1. **Conectar EEG**: Simula la conexión con tu banda cerebral
2. **<PERSON><PERSON><PERSON>**: Procesa las ondas capturadas durante el sueño
3. **Generar Cómic**: Elige un estilo y crea tu historieta personalizada
4. **Explorar Galería**: Revisa tu colección de sueños convertidos en cómics

## Estructura del Proyecto

```
├── app/
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/
│   ├── EEGConnector.tsx
│   ├── DreamAnalyzer.tsx
│   ├── ComicGenerator.tsx
│   └── DreamGallery.tsx
└── ...
```

## Funcionalidades Futuras

- Integración real con dispositivos EEG
- IA avanzada para interpretación de sueños
- Más estilos artísticos
- Exportación en diferentes formatos
- Compartir historietas en redes sociales
