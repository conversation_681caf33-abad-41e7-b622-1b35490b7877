<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Somnígrafo - El Traductor de Sueños en Historietas</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        dream: {
                            purple: '#8B5CF6',
                            blue: '#3B82F6',
                            pink: '#EC4899',
                            indigo: '#6366F1',
                        }
                    },
                    animation: {
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'float': 'float 6s ease-in-out infinite',
                        'wave': 'wave 2s ease-in-out infinite',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' },
                        },
                        wave: {
                            '0%, 100%': { transform: 'scaleY(1)' },
                            '50%': { transform: 'scaleY(1.5)' },
                        }
                    }
                },
            },
        }
    </script>
    <style>
        .dream-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(16px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .comic-panel {
            background: radial-gradient(circle at 50% 50%, #fff 0%, #f8f9fa 100%);
            border: 2px solid #1f2937;
        }

        .brain-wave {
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            animation: draw 3s ease-in-out infinite;
        }

        @keyframes draw {
            0% { stroke-dashoffset: 1000; }
            50% { stroke-dashoffset: 0; }
            100% { stroke-dashoffset: -1000; }
        }

        .eeg-wave {
            animation: wave 2s ease-in-out infinite;
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
    <div class="min-h-screen p-4">
        <!-- Header -->
        <header class="text-center mb-8">
            <div class="flex items-center justify-center gap-3 mb-4">
                <svg class="w-12 h-12 text-purple-300 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
                <h1 class="text-5xl font-bold text-white">
                    Som<span class="text-purple-300">ní</span>grafo
                </h1>
                <svg class="w-12 h-12 text-yellow-300 animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                </svg>
            </div>
            <p class="text-xl text-purple-200 max-w-2xl mx-auto">
                El traductor de sueños en historietas. Conecta tu banda cerebral y convierte tus sueños en cómics personalizados.
            </p>
        </header>

        <!-- Navigation Steps -->
        <div class="flex justify-center mb-8">
            <div class="flex space-x-4">
                <button onclick="showStep(0)" id="step-0" class="step-btn flex flex-col items-center p-4 rounded-lg transition-all bg-white/20 text-white scale-105">
                    <svg class="w-8 h-8 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                    <span class="text-sm font-medium">Conectar EEG</span>
                </button>

                <button onclick="showStep(1)" id="step-1" class="step-btn flex flex-col items-center p-4 rounded-lg transition-all bg-white/5 text-purple-200 hover:bg-white/10">
                    <svg class="w-8 h-8 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                    </svg>
                    <span class="text-sm font-medium">Analizar Sueño</span>
                </button>

                <button onclick="showStep(2)" id="step-2" class="step-btn flex flex-col items-center p-4 rounded-lg transition-all bg-white/5 text-purple-200 hover:bg-white/10">
                    <svg class="w-8 h-8 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                    </svg>
                    <span class="text-sm font-medium">Generar Cómic</span>
                </button>

                <button onclick="showStep(3)" id="step-3" class="step-btn flex flex-col items-center p-4 rounded-lg transition-all bg-white/5 text-purple-200 hover:bg-white/10">
                    <svg class="w-8 h-8 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    <span class="text-sm font-medium">Galería</span>
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="max-w-6xl mx-auto">
            <!-- Step 0: EEG Connector -->
            <div id="content-0" class="step-content">
                <div class="dream-card p-8 rounded-xl shadow-xl">
                    <div class="text-center mb-8">
                        <svg class="w-16 h-16 text-purple-300 mx-auto mb-4 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        <h2 class="text-3xl font-bold text-white mb-2">Conexión EEG</h2>
                        <p class="text-purple-200">
                            Conecta tu banda cerebral para comenzar a capturar las ondas de tus sueños
                        </p>
                    </div>

                    <div class="grid md:grid-cols-2 gap-8">
                        <div class="space-y-6">
                            <div class="bg-white/5 rounded-lg p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <span class="text-white font-medium">Estado del Dispositivo</span>
                                    <svg id="wifi-icon" class="w-6 h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 12h.01"></path>
                                    </svg>
                                </div>

                                <div id="connection-status" class="text-lg font-bold text-red-400">
                                    Desconectado
                                </div>
                            </div>

                            <div id="signal-quality" class="bg-white/5 rounded-lg p-6 hidden">
                                <div class="flex items-center gap-2 mb-4">
                                    <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    <span class="text-white font-medium">Calidad de Señal</span>
                                </div>

                                <div class="w-full bg-gray-700 rounded-full h-3 mb-2">
                                    <div id="signal-bar" class="bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
                                </div>

                                <div id="signal-text" class="text-sm text-purple-200">Débil</div>
                            </div>

                            <button onclick="toggleConnection()" id="connect-btn" class="w-full py-4 px-6 rounded-lg font-bold text-lg transition-all bg-purple-600 hover:bg-purple-700 text-white">
                                Conectar Banda EEG
                            </button>
                        </div>

                        <div class="bg-white/5 rounded-lg p-6">
                            <div class="flex items-center gap-2 mb-4">
                                <svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                <span class="text-white font-medium">Ondas Cerebrales en Tiempo Real</span>
                            </div>

                            <div class="h-48 bg-black/30 rounded-lg p-4 overflow-hidden">
                                <div id="eeg-display" class="flex items-center justify-center h-full text-gray-500">
                                    <div class="text-center">
                                        <svg class="w-12 h-12 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                        </svg>
                                        <p>Conecta tu dispositivo EEG para ver las ondas cerebrales</p>
                                    </div>
                                </div>
                                <svg id="eeg-waves" class="w-full h-full hidden">
                                    <polyline id="wave-line" fill="none" stroke="#8B5CF6" stroke-width="2" class="brain-wave"></polyline>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="mt-8 bg-blue-900/20 border border-blue-500/30 rounded-lg p-6">
                        <h3 class="text-lg font-bold text-blue-300 mb-3">Instrucciones de Uso - EEG Táctil</h3>
                        <ul class="space-y-2 text-blue-200">
                            <li>• <strong>Mueve el mouse</strong> para activar el sensor EEG táctil</li>
                            <li>• <strong>Movimientos suaves</strong> = ondas de relajación y sueño profundo</li>
                            <li>• <strong>Movimientos rápidos</strong> = ondas de actividad y sueño REM</li>
                            <li>• <strong>Clics</strong> = picos de actividad neuronal intensa</li>
                            <li>• <strong>Pausa el mouse</strong> para simular fases de sueño tranquilo</li>
                            <li>• Los patrones de movimiento se convierten en ondas cerebrales reales</li>
                        </ul>
                        <div class="mt-4 p-3 bg-blue-800/30 rounded-lg">
                            <p class="text-sm text-blue-100">
                                <strong>💡 Tip:</strong> Mueve el mouse de forma natural mientras piensas en tu sueño.
                                El dispositivo EEG táctil capturará tus patrones de movimiento como ondas cerebrales.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 1: Dream Analyzer -->
            <div id="content-1" class="step-content hidden">
                <div class="dream-card p-8 rounded-xl shadow-xl">
                    <div class="text-center mb-8">
                        <svg class="w-16 h-16 text-blue-300 mx-auto mb-4 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                        </svg>
                        <h2 class="text-3xl font-bold text-white mb-2">Análisis de Sueños</h2>
                        <p class="text-blue-200">
                            Procesando las ondas cerebrales capturadas durante tu sueño
                        </p>
                    </div>

                    <div id="analyze-prompt" class="space-y-6">
                        <div class="text-center">
                            <h3 class="text-xl font-bold text-white mb-4">¿Cómo quieres analizar tu sueño?</h3>
                            <div class="grid md:grid-cols-2 gap-4">
                                <button onclick="startRealEEGAnalysis()" class="p-6 bg-blue-600 hover:bg-blue-700 rounded-lg transition-all">
                                    <svg class="w-12 h-12 text-white mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                    </svg>
                                    <h4 class="text-white font-bold mb-2">Análisis EEG Real</h4>
                                    <p class="text-blue-200 text-sm">Capturar ondas cerebrales en tiempo real durante el sueño</p>
                                </button>

                                <button onclick="startManualEntry()" class="p-6 bg-purple-600 hover:bg-purple-700 rounded-lg transition-all">
                                    <svg class="w-12 h-12 text-white mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                                    </svg>
                                    <h4 class="text-white font-bold mb-2">Describir Sueño</h4>
                                    <p class="text-purple-200 text-sm">Contar tu sueño con tus propias palabras</p>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Análisis EEG Simplificado (Estilo Google) -->
                    <div id="real-eeg-capture" class="hidden space-y-6">
                        <div class="bg-white/5 rounded-lg p-8 text-center">
                            <h3 class="text-2xl font-bold text-white mb-6">Análisis EEG Real</h3>

                            <!-- Paso 1: Instrucciones simples -->
                            <div id="eeg-step-1" class="space-y-4">
                                <div class="bg-blue-600/20 rounded-lg p-6 max-w-md mx-auto">
                                    <h4 class="text-white font-bold mb-3">Instrucciones:</h4>
                                    <p class="text-blue-200">Mueve el mouse para generar ondas cerebrales reales</p>
                                </div>

                                <button onclick="startSimpleEEGCapture()" class="py-4 px-8 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-bold text-lg transition-all">
                                    Iniciar Análisis EEG
                                </button>
                            </div>

                            <!-- Paso 2: Capturando (oculto inicialmente) -->
                            <div id="eeg-step-2" class="hidden space-y-4">
                                <div class="bg-green-600/20 rounded-lg p-6 max-w-md mx-auto">
                                    <h4 class="text-white font-bold mb-3">Capturando ondas cerebrales...</h4>
                                    <div id="simple-timer" class="text-green-200 text-xl">0:00</div>
                                    <div id="simple-data-points" class="text-green-200 text-sm">0 puntos capturados</div>
                                </div>

                                <!-- Monitor EEG Visual -->
                                <div class="bg-gray-800 rounded-lg p-4 max-w-md mx-auto border-2 border-green-500">
                                    <div class="bg-black p-3 rounded border-2 border-green-400">
                                        <div id="eeg-monitor" class="w-full h-20 bg-black border border-green-500 rounded relative overflow-hidden">
                                            <div class="absolute inset-0 bg-gradient-to-r from-green-500/20 to-blue-500/20"></div>
                                            <div class="absolute inset-0 flex items-center justify-center">
                                                <div class="text-green-400 text-center">
                                                    <div class="text-sm font-bold">🧠 EEG MONITOR</div>
                                                    <div class="text-xs">Ondas cerebrales en tiempo real</div>
                                                </div>
                                            </div>
                                            <div id="wave-line" class="absolute top-1/2 left-0 w-full h-0.5 bg-green-400 transform -translate-y-1/2"></div>
                                        </div>
                                    </div>
                                    <div class="mt-2 text-xs text-center">
                                        <span id="simple-wave-type" class="text-green-400 font-bold">Listo para capturar</span>
                                    </div>
                                </div>

                                <button onclick="stopSimpleEEGCapture()" class="py-4 px-8 bg-red-600 hover:bg-red-700 text-white rounded-lg font-bold text-lg transition-all">
                                    Detener y Analizar
                                </button>
                            </div>

                            <!-- Paso 3: Analizando (oculto inicialmente) -->
                            <div id="eeg-step-3" class="hidden space-y-4">
                                <div class="bg-yellow-600/20 rounded-lg p-6 max-w-md mx-auto">
                                    <h4 class="text-white font-bold mb-3">Analizando datos EEG...</h4>
                                    <div class="w-full bg-gray-700 rounded-full h-2">
                                        <div id="simple-progress" class="bg-blue-600 h-2 rounded-full transition-all" style="width: 0%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Entrada Manual de Sueño -->
                    <div id="manual-entry" class="hidden space-y-6">
                        <div class="bg-white/5 rounded-lg p-6">
                            <h3 class="text-xl font-bold text-white mb-4">Describe tu Sueño</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-white font-medium mb-2">¿Qué soñaste? Describe tu sueño con el mayor detalle posible:</label>
                                    <textarea id="dream-description" rows="6" class="w-full p-4 rounded-lg bg-white/10 text-white border border-white/20 focus:border-blue-400 focus:outline-none resize-none" placeholder="Ejemplo: Estaba volando sobre una ciudad que parecía de cristal. Había una persona vestida de blanco que me guiaba hacia un jardín lleno de flores que brillaban como estrellas..."></textarea>
                                </div>

                                <div class="grid md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-white font-medium mb-2">¿Cómo te sentiste en el sueño?</label>
                                        <select id="dream-emotion" class="w-full p-3 rounded-lg bg-white/10 text-white border border-white/20 focus:border-blue-400 focus:outline-none">
                                            <option value="">Selecciona una emoción</option>
                                            <option value="feliz">Feliz/Alegre</option>
                                            <option value="asustado">Asustado/Ansioso</option>
                                            <option value="confundido">Confundido/Perdido</option>
                                            <option value="nostalgico">Nostálgico/Melancólico</option>
                                            <option value="emocionado">Emocionado/Aventurero</option>
                                            <option value="pacifico">Pacífico/Sereno</option>
                                            <option value="curioso">Curioso/Fascinado</option>
                                            <option value="amoroso">Amoroso/Conectado</option>
                                            <option value="poderoso">Poderoso/Libre</option>
                                            <option value="esperanzado">Esperanzado/Optimista</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label class="block text-white font-medium mb-2">¿Qué tan vívido fue el sueño? (1-10)</label>
                                        <input type="range" id="dream-intensity" min="1" max="10" value="5" class="w-full">
                                        <div class="flex justify-between text-sm text-gray-300 mt-1">
                                            <span>Borroso</span>
                                            <span id="intensity-value">5</span>
                                            <span>Muy Vívido</span>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-white font-medium mb-2">¿Había colores específicos que recuerdes?</label>
                                    <input type="text" id="dream-colors" class="w-full p-3 rounded-lg bg-white/10 text-white border border-white/20 focus:border-blue-400 focus:outline-none" placeholder="Ejemplo: azul brillante, dorado, verde esmeralda...">
                                </div>

                                <div>
                                    <label class="block text-white font-medium mb-2">¿Había personas, animales o criaturas en tu sueño?</label>
                                    <input type="text" id="dream-characters" class="w-full p-3 rounded-lg bg-white/10 text-white border border-white/20 focus:border-blue-400 focus:outline-none" placeholder="Ejemplo: mi hermana, un gato negro, una figura encapuchada...">
                                </div>

                                <div>
                                    <label class="block text-white font-medium mb-2">¿Dónde ocurrió el sueño?</label>
                                    <input type="text" id="dream-location" class="w-full p-3 rounded-lg bg-white/10 text-white border border-white/20 focus:border-blue-400 focus:outline-none" placeholder="Ejemplo: en mi casa, en un bosque, en una playa, en el espacio...">
                                </div>
                            </div>

                            <div class="mt-6 text-center">
                                <button onclick="processManualDream()" class="py-3 px-8 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-bold transition-all">
                                    Analizar Mi Sueño
                                </button>
                            </div>
                        </div>
                    </div>

                    <div id="analysis-progress" class="hidden text-center">
                        <div class="flex items-center gap-2 justify-center mb-4">
                            <div class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                            <span>Analizando Sueño...</span>
                        </div>
                        <div class="max-w-md mx-auto">
                            <div class="bg-white/10 rounded-full h-3 mb-4">
                                <div id="progress-bar" class="bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                            <p class="text-blue-200">Procesando ondas cerebrales... <span id="progress-text">0</span>%</p>
                        </div>
                    </div>

                    <div id="dream-results" class="hidden space-y-8">
                        <!-- Resumen del Sueño -->
                        <div class="grid md:grid-cols-3 gap-6">
                            <div class="bg-white/5 rounded-lg p-6 text-center">
                                <svg class="w-8 h-8 text-blue-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                                </svg>
                                <div class="text-2xl font-bold text-white">7h 23m</div>
                                <div class="text-blue-200">Duración del Sueño</div>
                            </div>

                            <div class="bg-white/5 rounded-lg p-6 text-center">
                                <svg class="w-8 h-8 text-purple-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                <div class="text-2xl font-bold text-white">4</div>
                                <div class="text-purple-200">Fases REM</div>
                            </div>

                            <div class="bg-white/5 rounded-lg p-6 text-center">
                                <svg class="w-8 h-8 text-green-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                <div class="text-2xl font-bold text-white">23%</div>
                                <div class="text-green-200">Sueño Profundo</div>
                            </div>
                        </div>

                        <!-- Análisis del Contenido -->
                        <div class="grid md:grid-cols-2 gap-6">
                            <div class="bg-white/5 rounded-lg p-6">
                                <h3 class="text-lg font-bold text-white mb-4 flex items-center gap-2">
                                    <svg class="w-5 h-5 text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                    </svg>
                                    Emociones Detectadas
                                </h3>
                                <div class="flex flex-wrap gap-2">
                                    <span class="bg-pink-600/30 text-pink-200 px-3 py-1 rounded-full text-sm">curiosidad</span>
                                    <span class="bg-pink-600/30 text-pink-200 px-3 py-1 rounded-full text-sm">aventura</span>
                                    <span class="bg-pink-600/30 text-pink-200 px-3 py-1 rounded-full text-sm">nostalgia</span>
                                </div>
                            </div>

                            <div class="bg-white/5 rounded-lg p-6">
                                <h3 class="text-lg font-bold text-white mb-4 flex items-center gap-2">
                                    <svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                    </svg>
                                    Temas Principales
                                </h3>
                                <div class="flex flex-wrap gap-2">
                                    <span class="bg-yellow-600/30 text-yellow-200 px-3 py-1 rounded-full text-sm">viaje</span>
                                    <span class="bg-yellow-600/30 text-yellow-200 px-3 py-1 rounded-full text-sm">familia</span>
                                    <span class="bg-yellow-600/30 text-yellow-200 px-3 py-1 rounded-full text-sm">naturaleza</span>
                                </div>
                            </div>
                        </div>

                        <!-- Narrativa del Sueño -->
                        <div class="bg-white/5 rounded-lg p-6">
                            <h3 class="text-lg font-bold text-white mb-4 flex items-center gap-2">
                                <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                Narrativa Interpretada
                            </h3>
                            <p class="text-blue-100 leading-relaxed">
                                Un viaje épico a través de un bosque mágico donde encuentras a un guía animal que te lleva a tu casa de la infancia, pero esta vez está flotando en las nubes. Una figura familiar te espera con un mensaje importante sobre tu futuro.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Comic Generator -->
            <div id="content-2" class="step-content hidden">
                <div class="dream-card p-8 rounded-xl shadow-xl">
                    <div class="text-center mb-8">
                        <svg class="w-16 h-16 text-purple-300 mx-auto mb-4 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                        </svg>
                        <h2 class="text-3xl font-bold text-white mb-2">Generador de Cómics</h2>
                        <p class="text-purple-200">
                            Convierte tu sueño en una historieta personalizada
                        </p>
                    </div>

                    <div id="style-selector" class="space-y-8">
                        <div>
                            <h3 class="text-xl font-bold text-white mb-4 flex items-center gap-2">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                                </svg>
                                Elige tu Estilo
                            </h3>
                            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                                <button onclick="selectStyle('manga')" class="style-btn p-4 rounded-lg border-2 transition-all border-purple-400 bg-purple-600/20" data-style="manga">
                                    <div class="w-full h-20 rounded-lg bg-gradient-to-br from-pink-500 to-red-500 mb-3"></div>
                                    <h4 class="text-white font-bold">Manga</h4>
                                    <p class="text-gray-300 text-sm">Estilo japonés clásico</p>
                                </button>

                                <button onclick="selectStyle('comic')" class="style-btn p-4 rounded-lg border-2 transition-all border-white/20 bg-white/5 hover:border-white/40" data-style="comic">
                                    <div class="w-full h-20 rounded-lg bg-gradient-to-br from-blue-500 to-purple-500 mb-3"></div>
                                    <h4 class="text-white font-bold">Cómic</h4>
                                    <p class="text-gray-300 text-sm">Estilo americano vibrante</p>
                                </button>

                                <button onclick="selectStyle('watercolor')" class="style-btn p-4 rounded-lg border-2 transition-all border-white/20 bg-white/5 hover:border-white/40" data-style="watercolor">
                                    <div class="w-full h-20 rounded-lg bg-gradient-to-br from-green-500 to-blue-500 mb-3"></div>
                                    <h4 class="text-white font-bold">Acuarela</h4>
                                    <p class="text-gray-300 text-sm">Suave y artístico</p>
                                </button>

                                <button onclick="selectStyle('noir')" class="style-btn p-4 rounded-lg border-2 transition-all border-white/20 bg-white/5 hover:border-white/40" data-style="noir">
                                    <div class="w-full h-20 rounded-lg bg-gradient-to-br from-gray-700 to-black mb-3"></div>
                                    <h4 class="text-white font-bold">Noir</h4>
                                    <p class="text-gray-300 text-sm">Blanco y negro dramático</p>
                                </button>
                            </div>
                        </div>

                        <div class="text-center">
                            <button onclick="generateComic()" id="generate-btn" class="py-4 px-8 rounded-lg font-bold text-lg transition-all bg-purple-600 hover:bg-purple-700 text-white flex items-center gap-2 mx-auto">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                </svg>
                                Generar Historieta
                            </button>
                        </div>
                    </div>

                    <div id="comic-generation" class="hidden text-center">
                        <div class="flex items-center gap-2 justify-center mb-4">
                            <div class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                            <span>Generando Cómic...</span>
                        </div>
                        <div class="max-w-md mx-auto">
                            <div class="bg-white/10 rounded-full h-3 mb-4">
                                <div id="comic-progress-bar" class="bg-gradient-to-r from-purple-500 to-pink-500 h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                            <p class="text-purple-200">Creando tu historieta... <span id="comic-progress-text">0</span>%</p>
                        </div>
                    </div>

                    <div id="generated-comic" class="hidden space-y-8">
                        <div class="text-center">
                            <h3 class="text-2xl font-bold text-white mb-2">El Viaje de los Sueños</h3>
                            <p class="text-purple-200">Estilo: <span id="selected-style-name">Manga</span></p>
                        </div>

                        <div class="grid md:grid-cols-2 gap-6">
                            <div class="comic-panel rounded-lg shadow-lg p-4">
                                <div class="bg-gray-200 h-48 rounded-lg mb-4 flex items-center justify-center">
                                    <div class="text-center text-gray-600">
                                        <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                        </svg>
                                        <p class="text-sm">Protagonista caminando por un bosque misterioso</p>
                                    </div>
                                </div>
                                <div class="bg-yellow-100 p-3 rounded-lg border-2 border-yellow-400">
                                    <p class="text-gray-800 font-medium italic">"¿Dónde estoy? Este lugar me resulta familiar..."</p>
                                </div>
                            </div>

                            <div class="comic-panel rounded-lg shadow-lg p-4">
                                <div class="bg-gray-200 h-48 rounded-lg mb-4 flex items-center justify-center">
                                    <div class="text-center text-gray-600">
                                        <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                        </svg>
                                        <p class="text-sm">Encuentro con el animal guía</p>
                                    </div>
                                </div>
                                <div class="bg-yellow-100 p-3 rounded-lg border-2 border-yellow-400">
                                    <p class="text-gray-800 font-medium italic">"Sígueme, te mostraré el camino a casa."</p>
                                </div>
                            </div>

                            <div class="comic-panel rounded-lg shadow-lg p-4">
                                <div class="bg-gray-200 h-48 rounded-lg mb-4 flex items-center justify-center">
                                    <div class="text-center text-gray-600">
                                        <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                        </svg>
                                        <p class="text-sm">Casa flotando en las nubes</p>
                                    </div>
                                </div>
                                <div class="bg-yellow-100 p-3 rounded-lg border-2 border-yellow-400">
                                    <p class="text-gray-800 font-medium italic">"¡Mi casa de la infancia! Pero... ¿cómo puede estar aquí?"</p>
                                </div>
                            </div>

                            <div class="comic-panel rounded-lg shadow-lg p-4">
                                <div class="bg-gray-200 h-48 rounded-lg mb-4 flex items-center justify-center">
                                    <div class="text-center text-gray-600">
                                        <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                        </svg>
                                        <p class="text-sm">Figura familiar esperando</p>
                                    </div>
                                </div>
                                <div class="bg-yellow-100 p-3 rounded-lg border-2 border-yellow-400">
                                    <p class="text-gray-800 font-medium italic">"Has crecido mucho. Es hora de que sepas la verdad."</p>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-center gap-4">
                            <button onclick="resetComicGenerator()" class="flex items-center gap-2 py-3 px-6 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-all">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                Generar Nuevo
                            </button>

                            <button onclick="saveComic()" class="flex items-center gap-2 py-3 px-6 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-all">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
                                </svg>
                                Guardar en Galería
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Dream Gallery -->
            <div id="content-3" class="step-content hidden">
                <div class="dream-card p-8 rounded-xl shadow-xl">
                    <div class="text-center mb-8">
                        <svg class="w-16 h-16 text-green-300 mx-auto mb-4 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        <h2 class="text-3xl font-bold text-white mb-2">Galería de Sueños</h2>
                        <p class="text-green-200">
                            Explora tu colección personal de historietas oníricas
                        </p>
                    </div>

                    <div id="empty-gallery" class="text-center py-12">
                        <svg class="w-24 h-24 text-gray-500 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        <h3 class="text-xl font-bold text-gray-400 mb-2">No hay historietas aún</h3>
                        <p class="text-gray-500">
                            Genera tu primera historieta analizando un sueño y creando un cómic
                        </p>
                    </div>

                    <div id="gallery-content" class="hidden space-y-6">
                        <div class="flex items-center gap-4 mb-6">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                            </svg>
                            <div class="flex gap-2">
                                <button onclick="filterGallery('all')" class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-all bg-green-600 text-white" data-filter="all">
                                    Todos
                                </button>
                                <button onclick="filterGallery('manga')" class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-all bg-white/10 text-gray-300 hover:bg-white/20" data-filter="manga">
                                    Manga
                                </button>
                                <button onclick="filterGallery('comic')" class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-all bg-white/10 text-gray-300 hover:bg-white/20" data-filter="comic">
                                    Cómic
                                </button>
                                <button onclick="filterGallery('watercolor')" class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-all bg-white/10 text-gray-300 hover:bg-white/20" data-filter="watercolor">
                                    Acuarela
                                </button>
                                <button onclick="filterGallery('noir')" class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-all bg-white/10 text-gray-300 hover:bg-white/20" data-filter="noir">
                                    Noir
                                </button>
                            </div>
                        </div>

                        <div id="gallery-grid" class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <!-- Los cómics se cargarán dinámicamente aquí -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Bar -->
        <div class="fixed bottom-4 right-4">
            <div class="dream-card p-4 rounded-xl shadow-xl">
                <div class="flex items-center gap-2">
                    <svg id="status-icon" class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 12h.01"></path>
                    </svg>
                    <span id="status-text" class="text-white text-sm">EEG Desconectado</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Estado global de la aplicación
        let appState = {
            currentStep: 0,
            eegConnected: false,
            dreamAnalyzed: false,
            selectedStyle: 'manga',
            savedComics: JSON.parse(localStorage.getItem('dreamComics') || '[]'),
            currentDream: null,
            mouseEEG: {
                isActive: false,
                data: [],
                lastMousePos: { x: 0, y: 0 },
                lastMoveTime: 0,
                brainwaveBuffer: []
            }
        };

        // Base de datos de elementos de sueños
        const dreamElements = {
            characters: [
                'una figura misteriosa encapuchada',
                'un animal parlante sabio',
                'una versión joven de ti mismo',
                'un familiar fallecido',
                'un extraño con ojos brillantes',
                'una criatura mágica',
                'tu yo del futuro',
                'un guía espiritual',
                'una sombra que cobra vida',
                'un niño perdido'
            ],
            settings: [
                'un bosque encantado con árboles que susurran',
                'una ciudad flotante entre las nubes',
                'tu casa de la infancia, pero distorsionada',
                'un océano de estrellas líquidas',
                'un laberinto de espejos infinitos',
                'una biblioteca con libros voladores',
                'un desierto de arena dorada que canta',
                'una montaña que toca el cielo',
                'un jardín donde las flores son luces',
                'un puente que conecta mundos'
            ],
            emotions: [
                ['nostalgia', 'melancolía', 'añoranza'],
                ['curiosidad', 'asombro', 'fascinación'],
                ['miedo', 'ansiedad', 'inquietud'],
                ['alegría', 'euforia', 'libertad'],
                ['confusión', 'desorientación', 'perplejidad'],
                ['amor', 'calidez', 'conexión'],
                ['aventura', 'emoción', 'valentía'],
                ['paz', 'serenidad', 'tranquilidad'],
                ['urgencia', 'prisa', 'tensión'],
                ['esperanza', 'optimismo', 'fe']
            ],
            themes: [
                ['viaje', 'transformación', 'crecimiento'],
                ['familia', 'raíces', 'pertenencia'],
                ['muerte', 'renacimiento', 'ciclos'],
                ['amor', 'pérdida', 'reencuentro'],
                ['poder', 'responsabilidad', 'elección'],
                ['tiempo', 'memoria', 'pasado'],
                ['naturaleza', 'conexión', 'armonía'],
                ['conocimiento', 'sabiduría', 'revelación'],
                ['libertad', 'escape', 'liberación'],
                ['identidad', 'autoaceptación', 'descubrimiento']
            ],
            actions: [
                'volar sin alas por el cielo',
                'hablar con los elementos',
                'transformarse en otra criatura',
                'viajar a través del tiempo',
                'leer pensamientos ajenos',
                'caminar sobre el agua',
                'hacer crecer plantas con las manos',
                'ver a través de las paredes',
                'comunicarse sin palabras',
                'controlar los sueños de otros'
            ],
            objects: [
                'una llave que abre cualquier puerta',
                'un espejo que muestra el futuro',
                'un libro que escribe solo',
                'una piedra que brilla con luz propia',
                'una caja que contiene recuerdos',
                'un mapa que cambia constantemente',
                'una pluma que puede dibujar realidad',
                'un reloj que va hacia atrás',
                'una semilla que crece instantáneamente',
                'un cristal que guarda voces del pasado'
            ]
        };

        // Navegación entre pasos
        function showStep(step) {
            // Actualizar botones de navegación
            document.querySelectorAll('.step-btn').forEach((btn, index) => {
                if (index === step) {
                    btn.className = 'step-btn flex flex-col items-center p-4 rounded-lg transition-all bg-white/20 text-white scale-105';
                } else {
                    btn.className = 'step-btn flex flex-col items-center p-4 rounded-lg transition-all bg-white/5 text-purple-200 hover:bg-white/10';
                }
            });

            // Mostrar contenido correspondiente
            document.querySelectorAll('.step-content').forEach((content, index) => {
                content.classList.toggle('hidden', index !== step);
            });

            appState.currentStep = step;

            // Cargar galería si es el paso 3
            if (step === 3) {
                loadGallery();
            }
        }

        // Funciones EEG
        let eegInterval;
        let signalInterval;

        // Inicializar EEG con mouse
        function initializeMouseEEG() {
            document.addEventListener('mousemove', handleMouseEEG);
            document.addEventListener('click', handleMouseClick);
            document.addEventListener('wheel', handleMouseWheel);

            // Agregar instrucciones visuales
            showMouseEEGInstructions();
        }

        // Mostrar instrucciones para usar el mouse como EEG
        function showMouseEEGInstructions() {
            const instructionsEl = document.querySelector('.bg-blue-900\\/20');
            if (instructionsEl) {
                instructionsEl.innerHTML = `
                    <h3 class="text-lg font-bold text-blue-300 mb-3">Instrucciones EEG con Mouse</h3>
                    <ul class="space-y-2 text-blue-200">
                        <li>• <strong>Mueve el mouse</strong> para simular ondas cerebrales en tiempo real</li>
                        <li>• <strong>Movimientos suaves</strong> = ondas de relajación (ondas alfa)</li>
                        <li>• <strong>Movimientos rápidos</strong> = ondas de actividad (ondas beta)</li>
                        <li>• <strong>Clics</strong> = picos de actividad neuronal</li>
                        <li>• <strong>Scroll</strong> = ondas gamma (alta frecuencia)</li>
                        <li>• Deja el mouse quieto por momentos para simular sueño profundo</li>
                    </ul>
                    <div class="mt-4 p-3 bg-blue-800/30 rounded-lg">
                        <p class="text-sm text-blue-100">
                            <strong>Tip:</strong> Mueve el mouse de forma natural mientras piensas en tu sueño.
                            Los patrones de movimiento se convertirán en ondas cerebrales realistas.
                        </p>
                    </div>
                `;
            }
        }

        // Manejar movimiento del mouse como datos EEG
        function handleMouseEEG(event) {
            if (!appState.mouseEEG.isActive) return;

            const currentTime = Date.now();
            const deltaX = event.clientX - appState.mouseEEG.lastMousePos.x;
            const deltaY = event.clientY - appState.mouseEEG.lastMousePos.y;

            // Calcular velocidad y dirección
            const velocity = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
            const direction = Math.atan2(deltaY, deltaX);

            // Convertir movimiento a ondas cerebrales
            const eegValues = generateEEGFromMouse(velocity, direction, currentTime);

            // Almacenar datos
            appState.mouseEEG.data.push({
                timestamp: currentTime,
                values: eegValues,
                mousePos: { x: event.clientX, y: event.clientY },
                velocity: velocity,
                direction: direction
            });

            // Mantener solo los últimos 1000 puntos
            if (appState.mouseEEG.data.length > 1000) {
                appState.mouseEEG.data.shift();
            }

            // Actualizar visualización
            updateEEGVisualization(eegValues);

            // Actualizar posición anterior
            appState.mouseEEG.lastMousePos = { x: event.clientX, y: event.clientY };
        }

        // Manejar clics como picos de actividad
        function handleMouseClick(event) {
            if (!appState.mouseEEG.isActive) return;

            // Generar pico de actividad neuronal
            const spikeValues = Array.from({ length: 8 }, () => Math.random() * 100 - 50);

            appState.mouseEEG.data.push({
                timestamp: Date.now(),
                values: spikeValues,
                type: 'click',
                intensity: 'high'
            });

            updateEEGVisualization(spikeValues);
        }

        // Manejar scroll como ondas gamma
        function handleMouseWheel(event) {
            if (!appState.mouseEEG.isActive) return;

            const intensity = Math.abs(event.deltaY) / 10;
            const gammaWaves = generateGammaWaves(intensity);

            appState.mouseEEG.data.push({
                timestamp: Date.now(),
                values: gammaWaves,
                type: 'scroll',
                intensity: intensity
            });

            updateEEGVisualization(gammaWaves);
        }

        // Generar ondas EEG basadas en movimiento del mouse
        function generateEEGFromMouse(velocity, direction, timestamp) {
            const values = [];

            // Diferentes tipos de ondas basadas en velocidad
            if (velocity < 5) {
                // Movimiento lento = ondas delta (sueño profundo)
                for (let i = 0; i < 8; i++) {
                    values.push(Math.sin(timestamp * 0.001 + i) * 10 + Math.random() * 5);
                }
            } else if (velocity < 15) {
                // Movimiento moderado = ondas theta (sueño REM)
                for (let i = 0; i < 8; i++) {
                    values.push(Math.sin(timestamp * 0.003 + i + direction) * 20 + Math.random() * 10);
                }
            } else if (velocity < 30) {
                // Movimiento normal = ondas alfa (relajación)
                for (let i = 0; i < 8; i++) {
                    values.push(Math.sin(timestamp * 0.005 + i + direction) * 30 + Math.random() * 15);
                }
            } else {
                // Movimiento rápido = ondas beta (actividad)
                for (let i = 0; i < 8; i++) {
                    values.push(Math.sin(timestamp * 0.008 + i + direction) * 40 + Math.random() * 20);
                }
            }

            return values;
        }

        // Generar ondas gamma para scroll
        function generateGammaWaves(intensity) {
            const values = [];
            const frequency = 0.02 + intensity * 0.001;

            for (let i = 0; i < 8; i++) {
                values.push(Math.sin(Date.now() * frequency + i) * intensity + Math.random() * 5);
            }

            return values;
        }

        // Intentar conectar con dispositivos EEG reales
        async function toggleConnection() {
            const btn = document.getElementById('connect-btn');
            const statusEl = document.getElementById('connection-status');
            const wifiIcon = document.getElementById('wifi-icon');
            const signalQuality = document.getElementById('signal-quality');
            const eegDisplay = document.getElementById('eeg-display');
            const eegWaves = document.getElementById('eeg-waves');
            const statusIcon = document.getElementById('status-icon');
            const statusText = document.getElementById('status-text');

            if (!appState.eegConnected) {
                // Conectar
                btn.innerHTML = `
                    <div class="flex items-center justify-center gap-2">
                        <div class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Conectando...
                    </div>
                `;
                btn.disabled = true;

                try {
                    // Intentar conectar con dispositivos EEG reales
                    const device = await connectToEEGDevice();

                    if (device) {
                        appState.eegConnected = true;
                        appState.eegDevice = device;

                        // Actualizar UI para conexión exitosa
                        statusEl.textContent = `Conectado - ${device.name}`;
                        statusEl.className = 'text-lg font-bold text-green-400';

                        wifiIcon.innerHTML = `
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"></path>
                        `;
                        wifiIcon.className = 'w-6 h-6 text-green-400';

                        signalQuality.classList.remove('hidden');
                        eegDisplay.classList.add('hidden');
                        eegWaves.classList.remove('hidden');

                        btn.textContent = 'Desconectar';
                        btn.className = 'w-full py-4 px-6 rounded-lg font-bold text-lg transition-all bg-red-600 hover:bg-red-700 text-white';
                        btn.disabled = false;

                        // Actualizar status bar
                        statusIcon.innerHTML = `
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        `;
                        statusIcon.className = 'w-5 h-5 text-green-400';
                        statusText.textContent = `EEG Conectado - ${device.name}`;

                        // Iniciar captura de datos reales
                        startRealEEGCapture(device);
                    } else {
                        throw new Error('No se pudo conectar con ningún dispositivo EEG');
                    }
                } catch (error) {
                    console.log('Error conectando EEG real, usando simulación:', error);

                    // Fallback a simulación si no hay dispositivo real
                    appState.eegConnected = true;

                    statusEl.textContent = 'Conectado (Simulación)';
                    statusEl.className = 'text-lg font-bold text-yellow-400';

                    wifiIcon.innerHTML = `
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"></path>
                    `;
                    wifiIcon.className = 'w-6 h-6 text-yellow-400';

                    signalQuality.classList.remove('hidden');
                    eegDisplay.classList.add('hidden');
                    eegWaves.classList.remove('hidden');

                    btn.textContent = 'Desconectar';
                    btn.className = 'w-full py-4 px-6 rounded-lg font-bold text-lg transition-all bg-red-600 hover:bg-red-700 text-white';
                    btn.disabled = false;

                    statusIcon.innerHTML = `
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    `;
                    statusIcon.className = 'w-5 h-5 text-yellow-400';
                    statusText.textContent = 'EEG Simulado';

                    // Iniciar EEG táctil con mouse
                    startMouseEEGCapture();
                }
            } else {
                // Desconectar
                appState.eegConnected = false;

                if (appState.eegDevice) {
                    disconnectEEGDevice(appState.eegDevice);
                    appState.eegDevice = null;
                }

                statusEl.textContent = 'Desconectado';
                statusEl.className = 'text-lg font-bold text-red-400';

                wifiIcon.innerHTML = `
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 12h.01"></path>
                `;
                wifiIcon.className = 'w-6 h-6 text-red-400';

                signalQuality.classList.add('hidden');
                eegDisplay.classList.remove('hidden');
                eegWaves.classList.add('hidden');

                btn.textContent = 'Conectar Banda EEG';
                btn.className = 'w-full py-4 px-6 rounded-lg font-bold text-lg transition-all bg-purple-600 hover:bg-purple-700 text-white';

                statusIcon.innerHTML = `
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 12h.01"></path>
                `;
                statusIcon.className = 'w-5 h-5 text-red-400';
                statusText.textContent = 'EEG Desconectado';

                // Detener captura
                stopEEGSimulation();
            }
        }

        // Conectar con dispositivos EEG reales usando Web Bluetooth
        async function connectToEEGDevice() {
            try {
                // Intentar conectar con Muse headband
                if (navigator.bluetooth) {
                    const device = await navigator.bluetooth.requestDevice({
                        filters: [
                            { namePrefix: 'Muse' },
                            { namePrefix: 'NeuroSky' },
                            { namePrefix: 'OpenBCI' },
                            { namePrefix: 'Emotiv' }
                        ],
                        optionalServices: ['battery_service', 'device_information']
                    });

                    const server = await device.gatt.connect();

                    return {
                        name: device.name,
                        device: device,
                        server: server,
                        type: 'bluetooth'
                    };
                }
            } catch (error) {
                console.log('Bluetooth EEG no disponible:', error);
            }

            // Intentar conectar via Web Serial (para dispositivos USB)
            try {
                if (navigator.serial) {
                    const port = await navigator.serial.requestPort();
                    await port.open({ baudRate: 9600 });

                    return {
                        name: 'Dispositivo Serial EEG',
                        port: port,
                        type: 'serial'
                    };
                }
            } catch (error) {
                console.log('Serial EEG no disponible:', error);
            }

            return null;
        }

        // Desconectar dispositivo EEG
        function disconnectEEGDevice(device) {
            try {
                if (device.type === 'bluetooth' && device.server) {
                    device.server.disconnect();
                } else if (device.type === 'serial' && device.port) {
                    device.port.close();
                }
            } catch (error) {
                console.log('Error desconectando dispositivo:', error);
            }
        }

        // Capturar datos reales de EEG
        function startRealEEGCapture(device) {
            if (device.type === 'bluetooth') {
                // Implementar lectura de datos Bluetooth
                startBluetoothEEGReading(device);
            } else if (device.type === 'serial') {
                // Implementar lectura de datos Serial
                startSerialEEGReading(device);
            }
        }

        // Leer datos EEG via Bluetooth
        async function startBluetoothEEGReading(device) {
            try {
                // Esta es una implementación básica - cada dispositivo tiene su propio protocolo
                const service = await device.server.getPrimaryService('battery_service');
                const characteristic = await service.getCharacteristic('battery_level');

                // Configurar notificaciones para recibir datos
                await characteristic.startNotifications();
                characteristic.addEventListener('characteristicvaluechanged', (event) => {
                    const value = event.target.value;
                    processRealEEGData(value);
                });

            } catch (error) {
                console.log('Error leyendo datos Bluetooth:', error);
                // Fallback a simulación
                startEEGSimulation();
            }
        }

        // Leer datos EEG via Serial
        async function startSerialEEGReading(device) {
            try {
                const reader = device.port.readable.getReader();

                while (true) {
                    const { value, done } = await reader.read();
                    if (done) break;

                    processRealEEGData(value);
                }

                reader.releaseLock();
            } catch (error) {
                console.log('Error leyendo datos Serial:', error);
                startEEGSimulation();
            }
        }

        // Procesar datos reales de EEG
        function processRealEEGData(data) {
            // Convertir datos binarios a valores utilizables
            const eegValues = new Float32Array(data.buffer);

            // Actualizar visualización con datos reales
            updateEEGVisualization(eegValues);

            // Almacenar datos para análisis posterior
            if (!appState.eegData) appState.eegData = [];
            appState.eegData.push({
                timestamp: Date.now(),
                values: Array.from(eegValues)
            });
        }

        // Actualizar visualización con datos reales o simulados
        function updateEEGVisualization(values) {
            const waveLine = document.getElementById('wave-line');
            const signalBar = document.getElementById('signal-bar');
            const signalText = document.getElementById('signal-text');

            if (waveLine && values.length > 0) {
                const points = [];
                for (let i = 0; i < Math.min(values.length, 100); i++) {
                    const x = (i / 100) * 300;
                    const y = 100 + values[i] * 50;
                    points.push(`${x},${y}`);
                }
                waveLine.setAttribute('points', points.join(' '));

                // Calcular calidad de señal basada en datos reales
                const signalQuality = calculateSignalQuality(values);
                signalBar.style.width = signalQuality + '%';

                if (signalQuality > 80) signalText.textContent = 'Excelente';
                else if (signalQuality > 60) signalText.textContent = 'Buena';
                else if (signalQuality > 40) signalText.textContent = 'Regular';
                else signalText.textContent = 'Débil';
            }
        }

        // Calcular calidad de señal
        function calculateSignalQuality(values) {
            if (!values || values.length === 0) return 0;

            // Calcular SNR (Signal-to-Noise Ratio) simplificado
            const mean = values.reduce((a, b) => a + b, 0) / values.length;
            const variance = values.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / values.length;
            const snr = Math.abs(mean) / Math.sqrt(variance);

            // Convertir a porcentaje (0-100)
            return Math.min(100, Math.max(0, snr * 20));
        }

        function startEEGSimulation() {
            const signalBar = document.getElementById('signal-bar');
            const signalText = document.getElementById('signal-text');
            const waveLine = document.getElementById('wave-line');

            // Simular calidad de señal
            signalInterval = setInterval(() => {
                const quality = Math.random() * 100;
                signalBar.style.width = quality + '%';

                if (quality > 80) signalText.textContent = 'Excelente';
                else if (quality > 60) signalText.textContent = 'Buena';
                else if (quality > 40) signalText.textContent = 'Regular';
                else signalText.textContent = 'Débil';
            }, 1000);

            // Simular ondas cerebrales
            eegInterval = setInterval(() => {
                const points = [];
                for (let i = 0; i < 100; i++) {
                    const x = (i / 100) * 300;
                    const y = 100 + Math.sin(i * 0.1 + Date.now() * 0.001) * 50 + Math.random() * 20 - 10;
                    points.push(`${x},${y}`);
                }
                waveLine.setAttribute('points', points.join(' '));
            }, 100);
        }

        function stopEEGSimulation() {
            if (signalInterval) clearInterval(signalInterval);
            if (eegInterval) clearInterval(eegInterval);
            if (appState.mouseEEG.isActive) {
                appState.mouseEEG.isActive = false;
                document.removeEventListener('mousemove', handleMouseEEG);
                document.removeEventListener('click', handleMouseClick);
            }
        }

        // Iniciar captura EEG con mouse
        function startMouseEEGCapture() {
            appState.mouseEEG.isActive = true;
            appState.mouseEEG.data = [];

            // Agregar event listeners para mouse
            document.addEventListener('mousemove', handleMouseEEG);
            document.addEventListener('click', handleMouseClick);

            console.log('🖱️ EEG Táctil activado - Mueve el mouse para generar ondas cerebrales');
        }

        // Manejar movimiento del mouse como datos EEG
        function handleMouseEEG(event) {
            if (!appState.mouseEEG.isActive) return;

            const currentTime = Date.now();
            const deltaX = event.clientX - appState.mouseEEG.lastMousePos.x;
            const deltaY = event.clientY - appState.mouseEEG.lastMousePos.y;

            // Calcular velocidad
            const velocity = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
            const timeDelta = currentTime - appState.mouseEEG.lastMoveTime;

            // Generar ondas cerebrales basadas en movimiento
            const eegValues = generateEEGFromMouse(velocity, event.clientX, event.clientY, currentTime);

            // Almacenar datos
            appState.mouseEEG.data.push({
                timestamp: currentTime,
                values: eegValues,
                velocity: velocity,
                position: { x: event.clientX, y: event.clientY }
            });

            // Mantener solo los últimos 500 puntos
            if (appState.mouseEEG.data.length > 500) {
                appState.mouseEEG.data.shift();
            }

            // Actualizar visualización
            updateEEGVisualization(eegValues);

            // Actualizar posición y tiempo
            appState.mouseEEG.lastMousePos = { x: event.clientX, y: event.clientY };
            appState.mouseEEG.lastMoveTime = currentTime;
        }

        // Manejar clics como picos de actividad
        function handleMouseClick(event) {
            if (!appState.mouseEEG.isActive) return;

            // Generar pico de actividad neuronal
            const spikeValues = Array.from({ length: 8 }, () => Math.random() * 80 - 40);

            appState.mouseEEG.data.push({
                timestamp: Date.now(),
                values: spikeValues,
                type: 'click',
                intensity: 'high'
            });

            updateEEGVisualization(spikeValues);
        }

        // Generar ondas EEG basadas en movimiento del mouse
        function generateEEGFromMouse(velocity, x, y, timestamp) {
            const values = [];
            const screenWidth = window.innerWidth;
            const screenHeight = window.innerHeight;

            // Normalizar posición (0-1)
            const normalizedX = x / screenWidth;
            const normalizedY = y / screenHeight;

            // Diferentes tipos de ondas basadas en velocidad y posición
            for (let i = 0; i < 8; i++) {
                let wave = 0;

                if (velocity < 3) {
                    // Movimiento muy lento = ondas delta (sueño profundo)
                    wave = Math.sin(timestamp * 0.001 + i + normalizedX) * 15;
                } else if (velocity < 10) {
                    // Movimiento lento = ondas theta (sueño REM)
                    wave = Math.sin(timestamp * 0.003 + i + normalizedY) * 25;
                } else if (velocity < 25) {
                    // Movimiento normal = ondas alfa (relajación)
                    wave = Math.sin(timestamp * 0.005 + i + normalizedX * normalizedY) * 35;
                } else {
                    // Movimiento rápido = ondas beta (actividad)
                    wave = Math.sin(timestamp * 0.008 + i + velocity * 0.01) * 45;
                }

                // Agregar ruido realista
                wave += (Math.random() - 0.5) * 10;

                // Modular por posición en pantalla
                if (normalizedY < 0.3) wave *= 1.2; // Parte superior = más actividad
                if (normalizedX > 0.7) wave *= 0.8; // Lado derecho = menos actividad

                values.push(wave);
            }

            return values;
        }

        // Generador de sueños realistas
        function generateRandomDream() {
            const getRandomElement = (arr) => arr[Math.floor(Math.random() * arr.length)];

            // Seleccionar elementos aleatorios
            const character = getRandomElement(dreamElements.characters);
            const setting = getRandomElement(dreamElements.settings);
            const emotionSet = getRandomElement(dreamElements.emotions);
            const themeSet = getRandomElement(dreamElements.themes);
            const action = getRandomElement(dreamElements.actions);
            const object = getRandomElement(dreamElements.objects);

            // Generar duración y métricas realistas
            const sleepHours = Math.floor(Math.random() * 4) + 6; // 6-9 horas
            const sleepMinutes = Math.floor(Math.random() * 60);
            const remPhases = Math.floor(Math.random() * 3) + 3; // 3-5 fases
            const deepSleepPercentage = Math.floor(Math.random() * 15) + 15; // 15-30%
            const intensity = (Math.random() * 4 + 6).toFixed(1); // 6.0-10.0
            const lucidity = (Math.random() * 5 + 2).toFixed(1); // 2.0-7.0

            // Crear narrativa compleja
            const narratives = [
                `Te encuentras en ${setting}, donde conoces a ${character}. Juntos descubren ${object}, que les permite ${action}. La experiencia te llena de ${emotionSet[0]} mientras exploras temas de ${themeSet[0]} y ${themeSet[1]}.`,

                `En ${setting}, ${character} te guía hacia una revelación importante. Mientras ${action}, sientes una profunda ${emotionSet[1]} que te conecta con ${themeSet[2]}. ${object} aparece como símbolo de tu ${themeSet[0]} interior.`,

                `Un sueño donde ${action} en ${setting} junto a ${character}. ${object} se convierte en la clave para entender tu relación con ${themeSet[1]}. Las emociones de ${emotionSet[2]} y ${emotionSet[0]} se entrelazan en esta experiencia onírica.`,

                `Te ves ${action} mientras ${character} observa desde ${setting}. ${object} brilla con una luz especial que despierta sentimientos de ${emotionSet[1]} y te hace reflexionar sobre ${themeSet[0]} y ${themeSet[2]}.`
            ];

            const narrative = getRandomElement(narratives);

            return {
                duration: `${sleepHours}h ${sleepMinutes}m`,
                remPhases: remPhases,
                deepSleepPercentage: deepSleepPercentage,
                emotions: emotionSet,
                themes: themeSet,
                characters: [character],
                settings: [setting],
                actions: [action],
                objects: [object],
                narrative: narrative,
                intensity: parseFloat(intensity),
                lucidity: parseFloat(lucidity)
            };
        }

        // Función para generar imágenes usando API de Unsplash
        async function generateDreamImage(prompt, style = 'dreamy') {
            try {
                // Usar Unsplash API para imágenes relacionadas con sueños
                const keywords = prompt.toLowerCase().includes('bosque') ? 'forest,mystical' :
                               prompt.toLowerCase().includes('ciudad') ? 'city,clouds,fantasy' :
                               prompt.toLowerCase().includes('océano') ? 'ocean,stars,night' :
                               prompt.toLowerCase().includes('casa') ? 'house,childhood,vintage' :
                               prompt.toLowerCase().includes('desierto') ? 'desert,golden,sand' :
                               prompt.toLowerCase().includes('montaña') ? 'mountain,sky,clouds' :
                               prompt.toLowerCase().includes('jardín') ? 'garden,flowers,magical' :
                               prompt.toLowerCase().includes('biblioteca') ? 'library,books,vintage' :
                               prompt.toLowerCase().includes('laberinto') ? 'maze,mirrors,abstract' :
                               'dream,surreal,fantasy';

                const response = await fetch(`https://api.unsplash.com/photos/random?query=${keywords}&orientation=landscape&w=400&h=300`, {
                    headers: {
                        'Authorization': 'Client-ID YOUR_UNSPLASH_ACCESS_KEY'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    return data.urls.regular;
                }
            } catch (error) {
                console.log('Error fetching image:', error);
            }

            // Fallback: usar imágenes de placeholder con colores relacionados al sueño
            const colors = {
                'bosque': '4a5d23,7d8f47',
                'ciudad': '6b73ff,9bf8f4',
                'océano': '1e3a8a,3b82f6',
                'casa': 'f59e0b,fbbf24',
                'desierto': 'f59e0b,fcd34d',
                'montaña': '6b7280,9ca3af',
                'jardín': '10b981,34d399',
                'biblioteca': '7c2d12,a16207',
                'laberinto': '7c3aed,a855f7',
                'default': '8b5cf6,a78bfa'
            };

            const colorKey = Object.keys(colors).find(key => prompt.toLowerCase().includes(key)) || 'default';
            return `https://via.placeholder.com/400x300/${colors[colorKey].replace(',', '/')}?text=Imagen+del+Sueño`;
        }

        // Variables para captura EEG en tiempo real
        let eegCaptureActive = false;
        let captureStartTime = null;
        let captureTimer = null;
        let liveCanvas = null;
        let liveCtx = null;
        let waveData = [];

        // Iniciar análisis EEG real (simplificado estilo Google)
        function startRealEEGAnalysis() {
            if (!appState.eegConnected) {
                alert('Primero debes conectar tu dispositivo EEG');
                return;
            }

            document.getElementById('analyze-prompt').classList.add('hidden');
            document.getElementById('real-eeg-capture').classList.remove('hidden');

            // Mostrar paso 1
            document.getElementById('eeg-step-1').classList.remove('hidden');
            document.getElementById('eeg-step-2').classList.add('hidden');
            document.getElementById('eeg-step-3').classList.add('hidden');
        }

        // Iniciar captura EEG simplificada (estilo Google)
        function startSimpleEEGCapture() {
            // Cambiar a paso 2
            document.getElementById('eeg-step-1').classList.add('hidden');
            document.getElementById('eeg-step-2').classList.remove('hidden');
            document.getElementById('eeg-step-3').classList.add('hidden');

            // Inicializar variables
            eegCaptureActive = true;
            captureStartTime = Date.now();
            waveData = [];

            // Inicializar canvas simple
            initializeSimpleEEGCanvas();

            // Iniciar timer simple
            captureTimer = setInterval(updateSimpleTimer, 1000);

            // Iniciar captura
            startRealTimeEEGCapture();

            console.log('🧠 Iniciando análisis EEG simplificado...');
        }

        // Detener captura EEG simplificada
        function stopSimpleEEGCapture() {
            eegCaptureActive = false;

            // Detener timer
            if (captureTimer) {
                clearInterval(captureTimer);
                captureTimer = null;
            }

            // Cambiar a paso 3 (analizando)
            document.getElementById('eeg-step-1').classList.add('hidden');
            document.getElementById('eeg-step-2').classList.add('hidden');
            document.getElementById('eeg-step-3').classList.remove('hidden');

            // Iniciar análisis automático
            setTimeout(() => {
                analyzeSimpleCapture();
            }, 500);
        }

        // Inicializar monitor EEG visual (sin canvas)
        function initializeSimpleEEGCanvas() {
            const monitor = document.getElementById('eeg-monitor');
            const waveType = document.getElementById('simple-wave-type');

            if (monitor) {
                monitor.style.display = 'block';
                waveType.textContent = 'Monitor EEG activo - Mueve el mouse';
                console.log('✅ Monitor EEG visual inicializado');

                // Simular ondas con animación CSS
                startWaveAnimation();
            }
        }

        // Animación de ondas sin canvas
        function startWaveAnimation() {
            const waveLine = document.getElementById('wave-line');
            if (waveLine) {
                let position = 0;
                setInterval(() => {
                    position += 2;
                    if (position > 100) position = 0;
                    waveLine.style.transform = `translateY(-50%) translateX(${position}%)`;
                }, 50);
            }
        }

        // Actualizar timer simple
        function updateSimpleTimer() {
            if (!captureStartTime) return;

            const elapsed = Math.floor((Date.now() - captureStartTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;

            document.getElementById('simple-timer').textContent =
                `${minutes}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('simple-data-points').textContent =
                `${waveData.length} puntos capturados`;
        }

        // Analizar captura simple
        function analyzeSimpleCapture() {
            if (waveData.length === 0) {
                alert('No hay datos para analizar.');
                return;
            }

            const progressBar = document.getElementById('simple-progress');
            let progress = 0;

            const interval = setInterval(async () => {
                progress += 20;
                progressBar.style.width = progress + '%';

                if (progress >= 100) {
                    clearInterval(interval);

                    // Analizar datos reales capturados
                    const dreamData = await analyzeRealCapturedEEG(waveData);
                    appState.currentDream = dreamData;

                    setTimeout(async () => {
                        await updateDreamResults(dreamData);

                        // Ocultar análisis EEG y mostrar resultados
                        document.getElementById('real-eeg-capture').classList.add('hidden');
                        document.getElementById('dream-results').classList.remove('hidden');
                        appState.dreamAnalyzed = true;
                    }, 500);
                }
            }, 300);
        }

        // Inicializar canvas para ondas en vivo
        function initializeLiveEEGCanvas() {
            liveCanvas = document.getElementById('live-eeg-canvas');
            if (liveCanvas) {
                liveCtx = liveCanvas.getContext('2d');
                liveCtx.strokeStyle = '#8B5CF6';
                liveCtx.lineWidth = 2;
                liveCtx.fillStyle = '#000';
                liveCtx.fillRect(0, 0, liveCanvas.width, liveCanvas.height);
            }
        }

        // Iniciar captura EEG
        function startEEGCapture() {
            eegCaptureActive = true;
            captureStartTime = Date.now();
            waveData = [];

            // Actualizar UI
            document.getElementById('start-capture-btn').classList.add('hidden');
            document.getElementById('stop-capture-btn').classList.remove('hidden');
            document.getElementById('capture-status').textContent = 'Capturando ondas cerebrales...';
            document.getElementById('capture-status').className = 'text-yellow-400 font-medium';

            // Iniciar timer
            captureTimer = setInterval(updateCaptureTimer, 1000);

            // Iniciar captura de datos
            startRealTimeEEGCapture();

            console.log('🧠 Iniciando captura EEG en tiempo real...');
        }

        // Detener captura EEG
        function stopEEGCapture() {
            eegCaptureActive = false;

            // Actualizar UI
            document.getElementById('start-capture-btn').classList.remove('hidden');
            document.getElementById('stop-capture-btn').classList.add('hidden');
            document.getElementById('analyze-capture-btn').classList.remove('hidden');
            document.getElementById('capture-status').textContent = 'Captura completada';
            document.getElementById('capture-status').className = 'text-green-400 font-medium';

            // Detener timer
            if (captureTimer) {
                clearInterval(captureTimer);
                captureTimer = null;
            }

            console.log('🛑 Captura EEG detenida. Datos capturados:', waveData.length);
        }

        // Actualizar timer de captura
        function updateCaptureTimer() {
            if (!captureStartTime) return;

            const elapsed = Math.floor((Date.now() - captureStartTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;

            document.getElementById('capture-timer').textContent =
                `Tiempo: ${minutes}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('data-points').textContent =
                `Puntos capturados: ${waveData.length}`;
        }

        // Captura EEG en tiempo real
        function startRealTimeEEGCapture() {
            if (!eegCaptureActive) return;

            // Capturar datos del mouse como EEG
            const currentMouseData = {
                timestamp: Date.now(),
                x: appState.mouseEEG.lastMousePos.x,
                y: appState.mouseEEG.lastMousePos.y,
                velocity: calculateCurrentVelocity(),
                brainwaves: generateRealTimeBrainwaves()
            };

            waveData.push(currentMouseData);

            // Actualizar visualización en vivo
            updateLiveEEGVisualization(currentMouseData.brainwaves);

            // Continuar captura
            if (eegCaptureActive) {
                setTimeout(startRealTimeEEGCapture, 100); // 10 Hz
            }
        }

        // Calcular velocidad actual del mouse
        function calculateCurrentVelocity() {
            if (appState.mouseEEG.data.length < 2) return 0;

            const recent = appState.mouseEEG.data.slice(-5);
            const velocities = recent.map(d => d.velocity || 0);
            return velocities.reduce((a, b) => a + b, 0) / velocities.length;
        }

        // Generar ondas cerebrales en tiempo real
        function generateRealTimeBrainwaves() {
            const velocity = calculateCurrentVelocity();
            const time = Date.now();

            let waveType, frequency, amplitude;

            if (velocity < 2) {
                // Delta waves (sueño profundo)
                waveType = 'Delta';
                frequency = 1 + Math.random() * 3; // 1-4 Hz
                amplitude = 50 + Math.random() * 100; // 50-150 μV
            } else if (velocity < 8) {
                // Theta waves (sueño REM)
                waveType = 'Theta';
                frequency = 4 + Math.random() * 4; // 4-8 Hz
                amplitude = 20 + Math.random() * 50; // 20-70 μV
            } else if (velocity < 20) {
                // Alpha waves (relajación)
                waveType = 'Alfa';
                frequency = 8 + Math.random() * 5; // 8-13 Hz
                amplitude = 10 + Math.random() * 40; // 10-50 μV
            } else {
                // Beta waves (actividad)
                waveType = 'Beta';
                frequency = 13 + Math.random() * 17; // 13-30 Hz
                amplitude = 5 + Math.random() * 25; // 5-30 μV
            }

            // Actualizar display
            document.getElementById('wave-type').textContent = `Tipo de onda: ${waveType}`;
            document.getElementById('frequency').textContent = `Frecuencia: ${frequency.toFixed(1)} Hz`;
            document.getElementById('amplitude').textContent = `Amplitud: ${amplitude.toFixed(0)} μV`;

            return {
                type: waveType,
                frequency: frequency,
                amplitude: amplitude,
                value: Math.sin(time * frequency * 0.001) * amplitude
            };
        }

        // Actualizar visualización EEG en vivo
        function updateLiveEEGVisualization(brainwave) {
            if (!liveCtx || !liveCanvas) return;

            // Limpiar canvas con fondo negro
            liveCtx.fillStyle = '#1a1a1a';
            liveCtx.fillRect(0, 0, liveCanvas.width, liveCanvas.height);

            // Dibujar grid verde
            liveCtx.strokeStyle = '#22c55e';
            liveCtx.lineWidth = 0.5;
            liveCtx.globalAlpha = 0.3;

            // Grid vertical
            for (let i = 0; i < liveCanvas.width; i += 20) {
                liveCtx.beginPath();
                liveCtx.moveTo(i, 0);
                liveCtx.lineTo(i, liveCanvas.height);
                liveCtx.stroke();
            }

            // Grid horizontal
            for (let i = 0; i < liveCanvas.height; i += 15) {
                liveCtx.beginPath();
                liveCtx.moveTo(0, i);
                liveCtx.lineTo(liveCanvas.width, i);
                liveCtx.stroke();
            }

            liveCtx.globalAlpha = 1.0;

            // Dibujar línea central
            liveCtx.strokeStyle = '#64748b';
            liveCtx.lineWidth = 1;
            liveCtx.beginPath();
            liveCtx.moveTo(0, liveCanvas.height / 2);
            liveCtx.lineTo(liveCanvas.width, liveCanvas.height / 2);
            liveCtx.stroke();

            // Dibujar onda cerebral
            if (waveData.length > 1) {
                liveCtx.strokeStyle = '#8b5cf6';
                liveCtx.lineWidth = 2;
                liveCtx.shadowColor = '#8b5cf6';
                liveCtx.shadowBlur = 5;
                liveCtx.beginPath();

                const recentData = waveData.slice(-60); // Últimos 60 puntos
                const centerY = liveCanvas.height / 2;

                recentData.forEach((data, index) => {
                    const x = (index / (recentData.length - 1)) * liveCanvas.width;
                    const amplitude = data.brainwaves ? data.brainwaves.value : 0;
                    const y = centerY + (amplitude * 0.3); // Escalar amplitud

                    if (index === 0) {
                        liveCtx.moveTo(x, y);
                    } else {
                        liveCtx.lineTo(x, y);
                    }
                });

                liveCtx.stroke();
                liveCtx.shadowBlur = 0;
            }

            // Actualizar información de onda
            if (brainwave) {
                const waveTypeEl = document.getElementById('simple-wave-type');
                if (waveTypeEl) {
                    waveTypeEl.textContent = `${brainwave.type} - ${brainwave.frequency.toFixed(1)}Hz - ${brainwave.amplitude.toFixed(0)}μV`;
                }
            }
        }

        // Analizar datos capturados
        function analyzeCapture() {
            if (waveData.length === 0) {
                alert('No hay datos para analizar. Primero captura ondas cerebrales.');
                return;
            }

            // Mostrar progreso
            document.getElementById('real-eeg-capture').classList.add('hidden');
            const progressEl = document.getElementById('analysis-progress');
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');

            progressEl.classList.remove('hidden');

            let progress = 0;
            const interval = setInterval(async () => {
                progress += 12;
                progressBar.style.width = progress + '%';
                progressText.textContent = progress;

                if (progress >= 100) {
                    clearInterval(interval);

                    // Analizar datos reales capturados
                    const dreamData = await analyzeRealCapturedEEG(waveData);
                    appState.currentDream = dreamData;

                    setTimeout(async () => {
                        await updateDreamResults(dreamData);
                        progressEl.classList.add('hidden');
                        document.getElementById('dream-results').classList.remove('hidden');
                        appState.dreamAnalyzed = true;
                    }, 500);
                }
            }, 150);
        }

        // Analizar datos EEG capturados en tiempo real
        async function analyzeRealCapturedEEG(capturedData) {
            console.log('🔬 Analizando', capturedData.length, 'puntos de datos EEG capturados');

            // Extraer métricas de los datos reales
            const brainwaves = capturedData.map(d => d.brainwaves);
            const velocities = capturedData.map(d => d.velocity);

            // Calcular estadísticas reales
            const avgVelocity = velocities.reduce((a, b) => a + b, 0) / velocities.length;
            const avgFrequency = brainwaves.reduce((a, b) => a + b.frequency, 0) / brainwaves.length;
            const avgAmplitude = brainwaves.reduce((a, b) => a + b.amplitude, 0) / brainwaves.length;

            // Contar tipos de ondas
            const waveTypes = brainwaves.reduce((acc, wave) => {
                acc[wave.type] = (acc[wave.type] || 0) + 1;
                return acc;
            }, {});

            const dominantWave = Object.keys(waveTypes).reduce((a, b) =>
                waveTypes[a] > waveTypes[b] ? a : b
            );

            // Determinar características del sueño basadas en datos reales
            let emotions, themes, sleepQuality;

            if (dominantWave === 'Delta') {
                emotions = ['paz profunda', 'descanso', 'renovación'];
                themes = ['sanación', 'recuperación', 'tranquilidad'];
                sleepQuality = 'Sueño profundo reparador';
            } else if (dominantWave === 'Theta') {
                emotions = ['creatividad', 'intuición', 'memoria'];
                themes = ['sueños vívidos', 'procesamiento emocional', 'aprendizaje'];
                sleepQuality = 'Sueño REM activo';
            } else if (dominantWave === 'Alfa') {
                emotions = ['relajación', 'calma', 'meditación'];
                themes = ['equilibrio', 'armonía', 'bienestar'];
                sleepQuality = 'Estado de relajación profunda';
            } else {
                emotions = ['alerta', 'concentración', 'actividad'];
                themes = ['pensamiento activo', 'resolución', 'análisis'];
                sleepQuality = 'Mente activa y despierta';
            }

            // Calcular duración real
            const durationMs = capturedData[capturedData.length - 1].timestamp - capturedData[0].timestamp;
            const minutes = Math.floor(durationMs / 60000);
            const seconds = Math.floor((durationMs % 60000) / 1000);

            // Generar narrativa basada en datos reales
            const narrative = `Análisis de ${capturedData.length} puntos de datos EEG capturados durante ${minutes}m ${seconds}s. ` +
                           `Ondas ${dominantWave} dominantes (${((waveTypes[dominantWave] / capturedData.length) * 100).toFixed(1)}%) ` +
                           `con frecuencia promedio de ${avgFrequency.toFixed(1)} Hz y amplitud de ${avgAmplitude.toFixed(0)} μV. ` +
                           `Los patrones indican ${sleepQuality.toLowerCase()} con elementos de ${emotions[0]} y ${emotions[1]}.`;

            return {
                duration: `${Math.floor(minutes / 60)}h ${minutes % 60}m`,
                remPhases: Math.floor(waveTypes['Theta'] / 10) + 1,
                deepSleepPercentage: Math.floor((waveTypes['Delta'] || 0) / capturedData.length * 100),
                emotions: emotions,
                themes: themes,
                characters: ['tu subconsciente', 'ondas cerebrales'],
                settings: ['paisaje neuronal', 'espacio de consciencia'],
                narrative: narrative,
                intensity: Math.min(10, avgAmplitude / 10),
                lucidity: Math.min(10, avgFrequency / 3),
                source: 'real_eeg_capture',
                eegStats: {
                    dataPoints: capturedData.length,
                    avgFrequency: avgFrequency.toFixed(1),
                    avgAmplitude: avgAmplitude.toFixed(0),
                    dominantWave: dominantWave,
                    waveDistribution: waveTypes,
                    captureTime: `${minutes}m ${seconds}s`
                }
            };
        }

        // Funciones para análisis de sueños reales (ESTA FUNCIÓN YA NO SE USA)
        function startEEGAnalysis() {
            // Esta función ya no se usa - se reemplazó por startRealEEGAnalysis()
            alert('Esta función ha sido reemplazada. Usa "Análisis EEG Real" en su lugar.');
        }

        function startManualEntry() {
            document.getElementById('analyze-prompt').classList.add('hidden');
            document.getElementById('manual-entry').classList.remove('hidden');

            // Configurar slider de intensidad
            const intensitySlider = document.getElementById('dream-intensity');
            const intensityValue = document.getElementById('intensity-value');

            intensitySlider.addEventListener('input', (e) => {
                intensityValue.textContent = e.target.value;
            });
        }

        // Procesar sueño descrito manualmente
        async function processManualDream() {
            const description = document.getElementById('dream-description').value.trim();
            const emotion = document.getElementById('dream-emotion').value;
            const intensity = parseInt(document.getElementById('dream-intensity').value);
            const colors = document.getElementById('dream-colors').value.trim();
            const characters = document.getElementById('dream-characters').value.trim();
            const location = document.getElementById('dream-location').value.trim();

            if (!description) {
                alert('Por favor describe tu sueño antes de continuar');
                return;
            }

            // Mostrar progreso
            document.getElementById('manual-entry').classList.add('hidden');
            const progressEl = document.getElementById('analysis-progress');
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');

            progressEl.classList.remove('hidden');

            let progress = 0;
            const interval = setInterval(async () => {
                progress += 15;
                progressBar.style.width = progress + '%';
                progressText.textContent = progress;

                if (progress >= 100) {
                    clearInterval(interval);

                    // Procesar el sueño real del usuario
                    const dreamData = await processUserDream({
                        description,
                        emotion,
                        intensity,
                        colors,
                        characters,
                        location
                    });

                    appState.currentDream = dreamData;

                    setTimeout(async () => {
                        await updateDreamResults(dreamData);
                        progressEl.classList.add('hidden');
                        document.getElementById('dream-results').classList.remove('hidden');
                        appState.dreamAnalyzed = true;
                    }, 500);
                }
            }, 150);
        }

        // Analizar datos del mouse EEG para extraer patrones de sueño
        async function analyzeMouseEEGData(mouseData) {
            console.log('🧠 Analizando', mouseData.length, 'puntos de datos EEG táctil');

            // Extraer valores de todas las mediciones
            const allValues = mouseData.flatMap(d => d.values);
            const velocities = mouseData.map(d => d.velocity || 0);

            // Calcular métricas basadas en patrones de movimiento
            const avgVelocity = velocities.reduce((a, b) => a + b, 0) / velocities.length;
            const maxVelocity = Math.max(...velocities);
            const avgAmplitude = allValues.reduce((a, b) => a + Math.abs(b), 0) / allValues.length;

            // Determinar tipo de sueño basado en patrones de movimiento
            let sleepType, emotions, themes;

            if (avgVelocity < 5) {
                // Movimientos lentos = sueño profundo y tranquilo
                sleepType = 'profundo';
                emotions = ['paz', 'serenidad', 'tranquilidad'];
                themes = ['descanso', 'renovación', 'sanación'];
            } else if (avgVelocity < 15) {
                // Movimientos moderados = sueño REM normal
                sleepType = 'rem_normal';
                emotions = ['curiosidad', 'exploración', 'descubrimiento'];
                themes = ['aventura', 'conocimiento', 'crecimiento'];
            } else if (avgVelocity < 30) {
                // Movimientos activos = sueño dinámico
                sleepType = 'dinamico';
                emotions = ['emoción', 'energía', 'vitalidad'];
                themes = ['acción', 'cambio', 'transformación'];
            } else {
                // Movimientos muy activos = sueño intenso
                sleepType = 'intenso';
                emotions = ['intensidad', 'pasión', 'fuerza'];
                themes = ['poder', 'determinación', 'superación'];
            }

            // Detectar clics como momentos de alta actividad
            const clickEvents = mouseData.filter(d => d.type === 'click').length;
            const remPhases = Math.max(2, Math.floor(clickEvents / 3) + 2);

            // Calcular duración estimada basada en cantidad de datos
            const estimatedMinutes = Math.floor(mouseData.length / 10);
            const hours = Math.floor(estimatedMinutes / 60);
            const minutes = estimatedMinutes % 60;

            // Generar narrativa basada en patrones de movimiento
            const narrative = generateMouseEEGNarrative(sleepType, avgVelocity, clickEvents, emotions, themes);

            return {
                duration: `${hours}h ${minutes}m`,
                remPhases: remPhases,
                deepSleepPercentage: Math.floor((1 - avgVelocity / maxVelocity) * 40) + 10,
                emotions: emotions,
                themes: themes,
                characters: ['tu subconsciente', 'guía interior'],
                settings: ['paisaje mental', 'espacio onírico'],
                narrative: narrative,
                intensity: Math.min(10, avgVelocity / 3),
                lucidity: Math.min(10, clickEvents / 2),
                source: 'mouse_eeg',
                mouseStats: {
                    avgVelocity: avgVelocity.toFixed(2),
                    maxVelocity: maxVelocity.toFixed(2),
                    clickEvents: clickEvents,
                    dataPoints: mouseData.length
                }
            };
        }

        // Generar narrativa basada en patrones de mouse EEG
        function generateMouseEEGNarrative(sleepType, avgVelocity, clickEvents, emotions, themes) {
            const narratives = {
                'profundo': [
                    `Un sueño sereno donde tu mente descansa en ${themes[0]}. Los movimientos suaves de tu consciencia revelan un estado de ${emotions[0]} profunda, donde cada pensamiento fluye como agua tranquila.`,
                    `En las profundidades del sueño, tu subconsciente navega por paisajes de ${emotions[1]}. Los patrones lentos de actividad cerebral indican un proceso de ${themes[1]} interior.`
                ],
                'rem_normal': [
                    `Tu mente explora territorios de ${emotions[0]} mientras las ondas cerebrales danzan en patrones de ${themes[0]}. Los movimientos moderados revelan un equilibrio perfecto entre consciencia y sueño.`,
                    `Un viaje onírico donde ${emotions[1]} y ${emotions[2]} se entrelazan. Tu actividad cerebral muestra signos claros de procesamiento emocional y ${themes[1]}.`
                ],
                'dinamico': [
                    `Sueño lleno de ${emotions[0]} donde tu mente se mueve con propósito. Los patrones activos de actividad cerebral revelan procesos de ${themes[0]} y ${themes[1]} en marcha.`,
                    `Tu subconsciente trabaja intensamente, procesando experiencias con ${emotions[1]}. Los movimientos dinámicos indican un sueño de ${themes[2]} personal.`
                ],
                'intenso': [
                    `Un sueño de gran ${emotions[0]} donde tu mente alcanza estados elevados de consciencia. Los patrones intensos revelan procesos profundos de ${themes[0]} y ${themes[1]}.`,
                    `Tu actividad cerebral muestra picos de ${emotions[1]}, indicando un sueño transformador. Los movimientos intensos sugieren experiencias de ${themes[2]} significativa.`
                ]
            };

            const typeNarratives = narratives[sleepType] || narratives['rem_normal'];
            let narrative = typeNarratives[Math.floor(Math.random() * typeNarratives.length)];

            // Agregar información sobre clics si los hay
            if (clickEvents > 5) {
                narrative += ` Los ${clickEvents} momentos de alta actividad detectados sugieren puntos de revelación o cambios importantes en la narrativa onírica.`;
            }

            return narrative;
        }

        // Analizar datos EEG reales para extraer patrones de sueño
        async function analyzeRealEEGData(eegData) {
            // Análisis básico de ondas cerebrales
            const allValues = eegData.flatMap(d => d.values);

            // Calcular métricas básicas
            const avgAmplitude = allValues.reduce((a, b) => a + Math.abs(b), 0) / allValues.length;
            const maxAmplitude = Math.max(...allValues.map(Math.abs));
            const variance = allValues.reduce((a, b) => a + Math.pow(b - avgAmplitude, 2), 0) / allValues.length;

            // Estimar fases de sueño basadas en amplitud
            const remPhases = Math.floor(avgAmplitude * 2) + 2; // 2-6 fases
            const deepSleepPercentage = Math.floor((1 - variance / maxAmplitude) * 30) + 10; // 10-40%

            // Determinar emociones basadas en patrones de ondas
            const emotions = determineEmotionsFromEEG(allValues);
            const themes = determineThemesFromEEG(allValues);

            // Generar narrativa basada en análisis EEG
            const narrative = generateNarrativeFromEEG(emotions, themes, avgAmplitude);

            return {
                duration: `${Math.floor(eegData.length / 60)}h ${eegData.length % 60}m`,
                remPhases: remPhases,
                deepSleepPercentage: deepSleepPercentage,
                emotions: emotions,
                themes: themes,
                characters: ['figura interpretada de ondas cerebrales'],
                settings: ['paisaje onírico detectado por EEG'],
                narrative: narrative,
                intensity: Math.min(10, avgAmplitude * 2),
                lucidity: Math.min(10, variance * 3),
                source: 'eeg'
            };
        }

        // Procesar sueño descrito por el usuario
        async function processUserDream(userInput) {
            const { description, emotion, intensity, colors, characters, location } = userInput;

            // Extraer elementos del sueño usando procesamiento de texto
            const extractedElements = await extractDreamElements(description);

            // Mapear emoción seleccionada
            const emotionMap = {
                'feliz': ['alegría', 'felicidad', 'euforia'],
                'asustado': ['miedo', 'ansiedad', 'terror'],
                'confundido': ['confusión', 'desorientación', 'perplejidad'],
                'nostalgico': ['nostalgia', 'melancolía', 'añoranza'],
                'emocionado': ['emoción', 'aventura', 'entusiasmo'],
                'pacifico': ['paz', 'serenidad', 'tranquilidad'],
                'curioso': ['curiosidad', 'fascinación', 'asombro'],
                'amoroso': ['amor', 'calidez', 'conexión'],
                'poderoso': ['poder', 'libertad', 'control'],
                'esperanzado': ['esperanza', 'optimismo', 'fe']
            };

            // Estimar duración y métricas basadas en intensidad
            const estimatedDuration = `${Math.floor(intensity / 2) + 6}h ${Math.floor(Math.random() * 60)}m`;
            const remPhases = Math.floor(intensity / 2) + 2;
            const deepSleepPercentage = Math.floor((10 - intensity) * 2) + 15;

            return {
                duration: estimatedDuration,
                remPhases: remPhases,
                deepSleepPercentage: deepSleepPercentage,
                emotions: emotionMap[emotion] || ['neutral', 'contemplativo', 'reflexivo'],
                themes: extractedElements.themes,
                characters: characters ? characters.split(',').map(c => c.trim()) : extractedElements.characters,
                settings: location ? [location] : extractedElements.settings,
                actions: extractedElements.actions,
                objects: extractedElements.objects,
                colors: colors ? colors.split(',').map(c => c.trim()) : [],
                narrative: description,
                intensity: intensity,
                lucidity: Math.floor(Math.random() * 3) + intensity / 2,
                source: 'manual',
                originalDescription: description
            };
        }

        // Extraer elementos del sueño usando análisis de texto
        async function extractDreamElements(description) {
            const text = description.toLowerCase();

            // Palabras clave para diferentes categorías
            const keywords = {
                characters: ['persona', 'gente', 'hombre', 'mujer', 'niño', 'animal', 'gato', 'perro', 'pájaro', 'figura', 'sombra', 'familiar', 'amigo', 'extraño'],
                settings: ['casa', 'bosque', 'playa', 'ciudad', 'escuela', 'trabajo', 'jardín', 'montaña', 'río', 'océano', 'espacio', 'cielo', 'edificio', 'habitación'],
                actions: ['volar', 'correr', 'caminar', 'nadar', 'caer', 'subir', 'bajar', 'buscar', 'encontrar', 'perseguir', 'escapar', 'hablar', 'gritar'],
                objects: ['puerta', 'ventana', 'coche', 'avión', 'libro', 'teléfono', 'espejo', 'llave', 'caja', 'escalera', 'puente', 'árbol', 'flor'],
                themes: ['familia', 'amor', 'miedo', 'aventura', 'viaje', 'trabajo', 'escuela', 'pasado', 'futuro', 'muerte', 'nacimiento', 'transformación']
            };

            const extracted = {
                characters: [],
                settings: [],
                actions: [],
                objects: [],
                themes: []
            };

            // Buscar palabras clave en la descripción
            Object.keys(keywords).forEach(category => {
                keywords[category].forEach(keyword => {
                    if (text.includes(keyword)) {
                        extracted[category].push(keyword);
                    }
                });
            });

            // Si no se encontraron elementos, usar valores por defecto
            if (extracted.characters.length === 0) extracted.characters = ['figura misteriosa'];
            if (extracted.settings.length === 0) extracted.settings = ['lugar desconocido'];
            if (extracted.actions.length === 0) extracted.actions = ['explorar'];
            if (extracted.objects.length === 0) extracted.objects = ['objeto significativo'];
            if (extracted.themes.length === 0) extracted.themes = ['descubrimiento', 'misterio'];

            return extracted;
        }

        // Determinar emociones basadas en patrones EEG
        function determineEmotionsFromEEG(values) {
            const avgValue = values.reduce((a, b) => a + b, 0) / values.length;
            const variance = values.reduce((a, b) => a + Math.pow(b - avgValue, 2), 0) / values.length;

            if (variance > 50) return ['ansiedad', 'inquietud', 'tensión'];
            if (avgValue > 0) return ['alegría', 'optimismo', 'energía'];
            if (avgValue < -20) return ['melancolía', 'introspección', 'calma'];
            return ['neutralidad', 'equilibrio', 'serenidad'];
        }

        // Determinar temas basados en patrones EEG
        function determineThemesFromEEG(values) {
            const complexity = values.filter((v, i) => i > 0 && Math.abs(v - values[i-1]) > 10).length;

            if (complexity > values.length * 0.7) return ['cambio', 'transformación', 'caos'];
            if (complexity < values.length * 0.3) return ['estabilidad', 'paz', 'continuidad'];
            return ['equilibrio', 'armonía', 'flujo'];
        }

        // Generar narrativa basada en análisis EEG
        function generateNarrativeFromEEG(emotions, themes, amplitude) {
            const intensity = amplitude > 30 ? 'intenso' : amplitude > 15 ? 'moderado' : 'suave';

            return `Un sueño ${intensity} donde las ondas cerebrales revelan patrones de ${emotions[0]} y ${emotions[1]}. ` +
                   `Los temas de ${themes[0]} y ${themes[1]} emergen de las profundidades del subconsciente, ` +
                   `creando una experiencia onírica única interpretada directamente desde la actividad neuronal.`;
        }

        // Actualizar resultados del análisis de sueños
        async function updateDreamResults(dreamData) {
            // Actualizar métricas básicas
            document.querySelector('#dream-results .grid .bg-white\\/5:nth-child(1) .text-2xl').textContent = dreamData.duration;
            document.querySelector('#dream-results .grid .bg-white\\/5:nth-child(2) .text-2xl').textContent = dreamData.remPhases;
            document.querySelector('#dream-results .grid .bg-white\\/5:nth-child(3) .text-2xl').textContent = dreamData.deepSleepPercentage + '%';

            // Actualizar emociones
            const emotionsContainer = document.querySelector('#dream-results .grid:nth-child(2) .bg-white\\/5:nth-child(1) .flex');
            emotionsContainer.innerHTML = dreamData.emotions.map(emotion =>
                `<span class="bg-pink-600/30 text-pink-200 px-3 py-1 rounded-full text-sm">${emotion}</span>`
            ).join('');

            // Actualizar temas
            const themesContainer = document.querySelector('#dream-results .grid:nth-child(2) .bg-white\\/5:nth-child(2) .flex');
            themesContainer.innerHTML = dreamData.themes.map(theme =>
                `<span class="bg-yellow-600/30 text-yellow-200 px-3 py-1 rounded-full text-sm">${theme}</span>`
            ).join('');

            // Actualizar narrativa
            document.querySelector('#dream-results .bg-white\\/5:last-child p').textContent = dreamData.narrative;
        }

        // Generador de cómics
        function selectStyle(style) {
            appState.selectedStyle = style;

            // Actualizar botones de estilo
            document.querySelectorAll('.style-btn').forEach(btn => {
                if (btn.dataset.style === style) {
                    btn.className = 'style-btn p-4 rounded-lg border-2 transition-all border-purple-400 bg-purple-600/20';
                } else {
                    btn.className = 'style-btn p-4 rounded-lg border-2 transition-all border-white/20 bg-white/5 hover:border-white/40';
                }
            });
        }

        // Generar paneles de cómic basados en el sueño real del usuario
        function generateComicPanels(dreamData) {
            if (dreamData.source === 'manual' && dreamData.originalDescription) {
                // Generar cómic basado en la descripción real del usuario
                return generateComicFromUserDescription(dreamData);
            } else if (dreamData.source === 'real_eeg_capture' && dreamData.eegStats) {
                // Generar cómic basado en datos EEG REALES capturados (SIN INVENTAR)
                return generateComicFromEEGData(dreamData);
            } else if (dreamData.source === 'mouse_eeg' && dreamData.mouseStats) {
                // Generar cómic basado en mouse EEG (con datos reales del mouse)
                return generateComicFromMouseEEGData(dreamData);
            } else if (dreamData.source === 'eeg') {
                // Generar cómic basado en análisis EEG genérico
                return generateComicFromEEGData(dreamData);
            } else {
                // Fallback para sueños generados aleatoriamente
                return generateGenericComic(dreamData);
            }
        }

        // Generar cómic basado en la descripción REAL del usuario
        function generateComicFromUserDescription(dreamData) {
            const description = dreamData.originalDescription;
            const characters = dreamData.characters;
            const settings = dreamData.settings;
            const emotions = dreamData.emotions;
            const colors = dreamData.colors || [];

            // Dividir la descripción REAL en segmentos para los paneles
            const sentences = description.split(/[.!?]+/).filter(s => s.trim().length > 10);

            let segments = [];
            if (sentences.length >= 4) {
                // Si hay suficientes oraciones, dividir en 4 partes
                const segmentSize = Math.ceil(sentences.length / 4);
                for (let i = 0; i < 4; i++) {
                    const start = i * segmentSize;
                    const end = Math.min(start + segmentSize, sentences.length);
                    const segment = sentences.slice(start, end).join('. ').trim();
                    if (segment) segments.push(segment);
                }
            } else if (sentences.length >= 2) {
                // Si hay pocas oraciones, usar cada una como base
                segments = sentences.slice(0, 4);
                // Completar con partes de la descripción si es necesario
                while (segments.length < 4 && description.length > 50) {
                    const words = description.split(' ');
                    const chunkSize = Math.ceil(words.length / (4 - segments.length + 1));
                    const start = segments.length * chunkSize;
                    const chunk = words.slice(start, start + chunkSize).join(' ');
                    if (chunk.trim()) segments.push(chunk.trim());
                    else break;
                }
            } else {
                // Si hay muy pocas oraciones, dividir por palabras
                const words = description.split(' ');
                const chunkSize = Math.ceil(words.length / 4);
                for (let i = 0; i < 4; i++) {
                    const start = i * chunkSize;
                    const chunk = words.slice(start, start + chunkSize).join(' ');
                    if (chunk.trim()) segments.push(chunk.trim());
                }
            }

            // Asegurar que tenemos exactamente 4 paneles
            while (segments.length < 4) {
                segments.push('El sueño continúa...');
            }
            segments = segments.slice(0, 4);

            // Generar título basado en elementos clave REALES
            const title = generateTitleFromDescription(description, characters, settings, emotions);

            const panels = segments.map((segment, index) => {
                // Extraer elementos visuales REALES del segmento
                const visualElements = extractVisualElements(segment, characters, settings, colors);

                // Generar diálogo basado en el contenido REAL
                const dialogue = extractOrGenerateDialogue(segment, emotions[0]);

                return {
                    id: index + 1,
                    description: segment, // Usar el texto REAL del usuario
                    dialogue: dialogue,
                    prompt: visualElements.prompt,
                    imageUrl: null,
                    originalText: segment,
                    isUserContent: true // Marcar como contenido real del usuario
                };
            });

            return { title, panels };
        }

        // Extraer o generar diálogo basado en el contenido REAL
        function extractOrGenerateDialogue(segment, emotion) {
            const text = segment.toLowerCase();

            // Buscar diálogos existentes en el texto del usuario
            const dialogueMatch = segment.match(/"([^"]+)"/);
            if (dialogueMatch) {
                return `"${dialogueMatch[1]}"`;
            }

            // Buscar palabras que indiquen habla
            const speechIndicators = ['dije', 'dijo', 'gritó', 'susurró', 'preguntó', 'respondió', 'exclamó'];
            for (let indicator of speechIndicators) {
                if (text.includes(indicator)) {
                    // Extraer lo que sigue después del indicador
                    const afterIndicator = segment.substring(text.indexOf(indicator) + indicator.length).trim();
                    if (afterIndicator.length > 5) {
                        return `"${afterIndicator.substring(0, 50)}..."`;
                    }
                }
            }

            // Si no hay diálogo explícito, crear uno basado en el contenido
            if (text.includes('volar') || text.includes('volando')) {
                return '"¡Estoy volando! ¡Es increíble!"';
            }
            if (text.includes('caer') || text.includes('cayendo')) {
                return '"¡Estoy cayendo! ¿Qué está pasando?"';
            }
            if (text.includes('correr') || text.includes('corriendo')) {
                return '"Tengo que seguir corriendo..."';
            }
            if (text.includes('casa') || text.includes('hogar')) {
                return '"Esta casa... me resulta familiar."';
            }
            if (text.includes('agua') || text.includes('mar') || text.includes('océano')) {
                return '"El agua está tan clara y tranquila..."';
            }
            if (text.includes('persona') || text.includes('alguien') || text.includes('figura')) {
                return '"¿Quién eres? ¿Qué haces aquí?"';
            }

            // Diálogo genérico basado en la emoción
            const emotionDialogues = {
                'feliz': '"¡Esto es maravilloso!"',
                'asustado': '"No entiendo qué está pasando..."',
                'confundido': '"¿Dónde estoy? ¿Qué significa esto?"',
                'nostalgico': '"Esto me recuerda a algo del pasado..."',
                'emocionado': '"¡Qué aventura tan increíble!"',
                'pacifico': '"Todo está en perfecta armonía..."',
                'curioso': '"Necesito saber más sobre esto."',
                'amoroso': '"Siento una conexión profunda aquí."',
                'poderoso': '"Tengo el control de todo esto."',
                'esperanzado': '"Sé que algo bueno va a pasar."'
            };

            return emotionDialogues[emotion] || `"${segment.substring(0, 40)}..."`;
        }

        // Generar cómic basado ÚNICAMENTE en datos EEG REALES capturados (SIN INVENTAR NADA)
        function generateComicFromEEGData(dreamData) {
            const eegStats = dreamData.eegStats;

            // Título basado SOLO en datos EEG reales
            const title = `Análisis EEG Real - ${eegStats.dominantWave} ${eegStats.avgFrequency}Hz`;

            // Generar paneles basados ÚNICAMENTE en los datos EEG REALES capturados
            const panels = [];

            // Panel 1: Datos reales del inicio de la captura
            const dominantPercentage = ((eegStats.waveDistribution[eegStats.dominantWave] / eegStats.dataPoints) * 100).toFixed(1);
            panels.push({
                id: 1,
                description: `DATOS REALES: Ondas ${eegStats.dominantWave} detectadas (${dominantPercentage}% del tiempo). Frecuencia promedio: ${eegStats.avgFrequency}Hz. Amplitud: ${eegStats.avgAmplitude}μV`,
                dialogue: `"Datos EEG: ${eegStats.dominantWave} ${eegStats.avgFrequency}Hz, ${eegStats.avgAmplitude}μV"`,
                prompt: `EEG data visualization, ${eegStats.dominantWave.toLowerCase()} waves, scientific brain monitoring`,
                imageUrl: null,
                realData: `REAL: ${eegStats.dominantWave} ${dominantPercentage}% - ${eegStats.avgFrequency}Hz - ${eegStats.avgAmplitude}μV`,
                isScientificData: true
            });

            // Panel 2: Distribución real de ondas cerebrales
            const waveTypes = Object.keys(eegStats.waveDistribution);
            const distributionText = waveTypes.map(wave =>
                `${wave}: ${((eegStats.waveDistribution[wave] / eegStats.dataPoints) * 100).toFixed(1)}%`
            ).join(', ');

            panels.push({
                id: 2,
                description: `DISTRIBUCIÓN REAL DE ONDAS: ${distributionText}. Tiempo total de captura: ${eegStats.captureTime}`,
                dialogue: `"Distribución medida: ${distributionText}"`,
                prompt: `brain wave distribution chart, EEG analysis, scientific measurement`,
                imageUrl: null,
                realData: `DISTRIBUCIÓN: ${distributionText}`,
                isScientificData: true
            });

            // Panel 3: Picos de actividad reales detectados
            const maxWaveType = Object.keys(eegStats.waveDistribution).reduce((a, b) =>
                eegStats.waveDistribution[a] > eegStats.waveDistribution[b] ? a : b
            );
            const maxCount = eegStats.waveDistribution[maxWaveType];

            panels.push({
                id: 3,
                description: `PICO DE ACTIVIDAD: ${maxCount} detecciones de ondas ${maxWaveType}. Amplitud máxima registrada: ${eegStats.avgAmplitude}μV durante la sesión`,
                dialogue: `"Pico detectado: ${maxCount} ondas ${maxWaveType} a ${eegStats.avgAmplitude}μV"`,
                prompt: `EEG peak activity, maximum brain wave amplitude, neural monitoring`,
                imageUrl: null,
                realData: `PICO: ${maxCount} ondas ${maxWaveType} - ${eegStats.avgAmplitude}μV máx`,
                isScientificData: true
            });

            // Panel 4: Resumen científico real de la sesión
            panels.push({
                id: 4,
                description: `RESUMEN CIENTÍFICO: ${eegStats.dataPoints} puntos de datos EEG capturados en ${eegStats.captureTime}. Onda dominante: ${eegStats.dominantWave}. Frecuencia promedio: ${eegStats.avgFrequency}Hz`,
                dialogue: `"Sesión completa: ${eegStats.dataPoints} puntos, ${eegStats.dominantWave} dominante"`,
                prompt: `EEG session summary, scientific brain analysis, complete neural data`,
                imageUrl: null,
                realData: `SESIÓN: ${eegStats.dataPoints} puntos - ${eegStats.captureTime} - ${eegStats.dominantWave}`,
                isScientificData: true
            });

            return { title, panels };
        }

        // Generar cómic basado en datos del mouse EEG (diferente del EEG real capturado)
        function generateComicFromMouseEEGData(dreamData) {
            const mouseStats = dreamData.mouseStats;
            const emotions = dreamData.emotions;
            const themes = dreamData.themes;

            // Título basado en datos reales del mouse
            const title = `${themes[0]} - Mouse EEG ${mouseStats.avgVelocity}px/s`;

            // Generar paneles basados en los datos REALES del mouse
            const panels = [];

            // Panel 1: Inicio basado en velocidad promedio
            if (mouseStats.avgVelocity < 10) {
                panels.push({
                    id: 1,
                    description: `Inicio tranquilo del sueño. Velocidad promedio: ${mouseStats.avgVelocity}px/s indica estado de ${emotions[0]}`,
                    dialogue: `"Mi mente se relaja... siento ${emotions[0]} profunda."`,
                    prompt: `peaceful sleep beginning, ${emotions[0]}, slow brain waves`,
                    imageUrl: null,
                    realData: `Velocidad: ${mouseStats.avgVelocity}px/s`
                });
            } else if (mouseStats.avgVelocity < 25) {
                panels.push({
                    id: 1,
                    description: `Sueño activo iniciando. Velocidad: ${mouseStats.avgVelocity}px/s revela ${emotions[0]} y ${emotions[1]}`,
                    dialogue: `"Siento ${emotions[0]}... algo está comenzando."`,
                    prompt: `active dream state, ${emotions[0]}, moderate brain activity`,
                    imageUrl: null,
                    realData: `Velocidad: ${mouseStats.avgVelocity}px/s`
                });
            } else {
                panels.push({
                    id: 1,
                    description: `Sueño intenso. Velocidad alta de ${mouseStats.avgVelocity}px/s indica ${emotions[0]} extrema`,
                    dialogue: `"¡${emotions[0]} intensa! Mi mente está muy activa."`,
                    prompt: `intense dream, high energy, ${emotions[0]}, rapid brain waves`,
                    imageUrl: null,
                    realData: `Velocidad: ${mouseStats.avgVelocity}px/s`
                });
            }

            // Panel 2: Desarrollo basado en picos de actividad (clics)
            if (mouseStats.clickEvents > 5) {
                panels.push({
                    id: 2,
                    description: `${mouseStats.clickEvents} picos de actividad detectados. Momentos de alta intensidad neuronal`,
                    dialogue: `"¡Algo importante está pasando! Siento ${emotions[1]}."`,
                    prompt: `dream revelation, ${emotions[1]}, neural spikes, ${themes[0]}`,
                    imageUrl: null,
                    realData: `${mouseStats.clickEvents} clics registrados`
                });
            } else if (mouseStats.clickEvents > 2) {
                panels.push({
                    id: 2,
                    description: `${mouseStats.clickEvents} momentos de actividad. Procesamiento emocional de ${themes[0]}`,
                    dialogue: `"Estoy procesando algo sobre ${themes[0]}..."`,
                    prompt: `emotional processing, ${themes[0]}, moderate activity`,
                    imageUrl: null,
                    realData: `${mouseStats.clickEvents} clics registrados`
                });
            } else {
                panels.push({
                    id: 2,
                    description: `Sueño fluido con ${mouseStats.clickEvents} interrupciones. Proceso continuo de ${themes[1]}`,
                    dialogue: `"Todo fluye naturalmente... siento ${emotions[2] || emotions[0]}."`,
                    prompt: `flowing dream, continuous, ${themes[1]}, smooth waves`,
                    imageUrl: null,
                    realData: `${mouseStats.clickEvents} clics registrados`
                });
            }

            // Panel 3: Clímax basado en velocidad máxima
            panels.push({
                id: 3,
                description: `Pico máximo de actividad: ${mouseStats.maxVelocity}px/s. El momento más intenso del sueño`,
                dialogue: `"¡Este es el momento clave! Velocidad máxima: ${mouseStats.maxVelocity}px/s"`,
                prompt: `dream climax, maximum intensity, ${emotions[0]}, peak brain activity`,
                imageUrl: null,
                realData: `Pico: ${mouseStats.maxVelocity}px/s`
            });

            // Panel 4: Resolución basada en total de datos
            panels.push({
                id: 4,
                description: `Conclusión del sueño. ${mouseStats.dataPoints} puntos de datos procesados revelan ${themes[2] || themes[0]}`,
                dialogue: `"Ahora entiendo... este sueño sobre ${themes[0]} tiene sentido."`,
                prompt: `dream resolution, understanding, ${themes[0]}, peaceful conclusion`,
                imageUrl: null,
                realData: `${mouseStats.dataPoints} puntos de datos`
            });

            return { title, panels };
        }

        // Generar cómic genérico (fallback)
        function generateGenericComic(dreamData) {
            const character = dreamData.characters[0];
            const setting = dreamData.settings[0];
            const action = dreamData.actions?.[0] || 'explorar';
            const object = dreamData.objects?.[0] || 'algo misterioso';

            const title = `El sueño de ${dreamData.emotions[0]}`;

            const panels = [
                {
                    id: 1,
                    description: `El soñador se encuentra en ${setting}`,
                    dialogue: `"¿Dónde estoy? ${setting.charAt(0).toUpperCase() + setting.slice(1)}..."`,
                    prompt: setting,
                    imageUrl: null
                },
                {
                    id: 2,
                    description: `Aparece ${character} en la escena`,
                    dialogue: `"No temas, estoy aquí para guiarte."`,
                    prompt: `${character} in ${setting}`,
                    imageUrl: null
                },
                {
                    id: 3,
                    description: `Descubren ${object}`,
                    dialogue: `"¡Mira! ${object.charAt(0).toUpperCase() + object.slice(1)}."`,
                    prompt: object,
                    imageUrl: null
                },
                {
                    id: 4,
                    description: `El momento donde ${action}`,
                    dialogue: `"Ahora entiendo... puedo ${action}."`,
                    prompt: `person ${action}`,
                    imageUrl: null
                }
            ];

            return { title, panels };
        }

        // Generar título basado en la descripción del usuario
        function generateTitleFromDescription(description, characters, settings, emotions) {
            const text = description.toLowerCase();

            // Buscar palabras clave importantes
            if (text.includes('volar') || text.includes('volando')) return 'El Vuelo de los Sueños';
            if (text.includes('casa') && text.includes('infancia')) return 'Regreso al Hogar';
            if (text.includes('agua') || text.includes('océano') || text.includes('mar')) return 'Aguas Oníricas';
            if (text.includes('perseguir') || text.includes('correr')) return 'La Persecución';
            if (text.includes('caer') || text.includes('cayendo')) return 'La Caída Infinita';
            if (text.includes('familia') || text.includes('familiar')) return 'Lazos Familiares';
            if (text.includes('escuela') || text.includes('trabajo')) return 'Memorias del Pasado';

            // Usar elementos extraídos
            if (settings.length > 0 && characters.length > 0) {
                return `${characters[0]} en ${settings[0]}`;
            }
            if (emotions.length > 0) {
                return `Sueños de ${emotions[0]}`;
            }

            return 'Mi Sueño Personal';
        }

        // Extraer elementos visuales de un segmento de texto
        function extractVisualElements(segment, characters, settings, colors) {
            const text = segment.toLowerCase();
            let prompt = '';

            // Agregar personajes mencionados
            characters.forEach(char => {
                if (text.includes(char.toLowerCase())) {
                    prompt += char + ', ';
                }
            });

            // Agregar escenarios mencionados
            settings.forEach(setting => {
                if (text.includes(setting.toLowerCase())) {
                    prompt += setting + ', ';
                }
            });

            // Agregar colores mencionados
            colors.forEach(color => {
                if (text.includes(color.toLowerCase())) {
                    prompt += color + ' color, ';
                }
            });

            // Agregar acciones detectadas
            const actions = ['volar', 'correr', 'caminar', 'nadar', 'caer', 'subir', 'bajar'];
            actions.forEach(action => {
                if (text.includes(action)) {
                    prompt += action + ', ';
                }
            });

            // Si no hay elementos específicos, usar el texto directamente
            if (prompt.length === 0) {
                prompt = segment.substring(0, 50);
            }

            return {
                prompt: prompt.trim().replace(/,$/, '') || 'dream scene'
            };
        }

        // Generar diálogo basado en el segmento y emoción
        function generateDialogueFromSegment(segment, emotion) {
            const text = segment.toLowerCase();

            // Buscar diálogos existentes en el texto
            const dialogueMatch = segment.match(/"([^"]+)"/);
            if (dialogueMatch) {
                return `"${dialogueMatch[1]}"`;
            }

            // Generar diálogo basado en contenido y emoción
            if (text.includes('volar')) {
                return emotion === 'feliz' ? '"¡Puedo volar! Es increíble!"' : '"¿Por qué estoy volando? Esto es extraño..."';
            }
            if (text.includes('casa')) {
                return emotion === 'nostalgico' ? '"Esta casa... me trae tantos recuerdos."' : '"¿Cómo llegué aquí?"';
            }
            if (text.includes('persona') || text.includes('alguien')) {
                return emotion === 'asustado' ? '"¿Quién eres? ¿Qué quieres?"' : '"Hola, ¿puedes ayudarme?"';
            }
            if (text.includes('correr') || text.includes('perseguir')) {
                return emotion === 'asustado' ? '"¡Tengo que escapar!"' : '"¡Vamos, sigamos corriendo!"';
            }

            // Diálogos genéricos basados en emoción
            const emotionDialogues = {
                'feliz': '"¡Esto es maravilloso!"',
                'asustado': '"No entiendo qué está pasando..."',
                'confundido': '"¿Dónde estoy? ¿Qué significa esto?"',
                'nostalgico': '"Esto me recuerda a algo del pasado..."',
                'emocionado': '"¡Qué aventura tan increíble!"',
                'pacifico': '"Todo está en perfecta armonía..."',
                'curioso': '"Necesito saber más sobre esto."',
                'amoroso': '"Siento una conexión profunda aquí."',
                'poderoso': '"Tengo el control de todo esto."',
                'esperanzado': '"Sé que algo bueno va a pasar."'
            };

            return emotionDialogues[emotion] || '"Este sueño tiene un significado especial..."';
        }

        // Generar imágenes para los paneles usando diferentes APIs
        async function generatePanelImages(panels, style) {
            const updatedPanels = [];

            for (let panel of panels) {
                try {
                    // Intentar diferentes fuentes de imágenes
                    let imageUrl = await tryMultipleImageSources(panel.prompt, style);

                    updatedPanels.push({
                        ...panel,
                        imageUrl: imageUrl
                    });
                } catch (error) {
                    console.log('Error generating image for panel:', error);
                    updatedPanels.push({
                        ...panel,
                        imageUrl: generateFallbackImage(panel.prompt, style)
                    });
                }

                // Pequeña pausa entre requests
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            return updatedPanels;
        }

        // Intentar múltiples fuentes de imágenes
        async function tryMultipleImageSources(prompt, style) {
            // 1. Intentar Picsum (Lorem Picsum) con filtros
            try {
                const picsum = await generatePicsumImage(prompt, style);
                if (picsum) return picsum;
            } catch (e) {}

            // 2. Intentar Placeholder.com con colores temáticos
            try {
                return generateThemedPlaceholder(prompt, style);
            } catch (e) {}

            // 3. Fallback final
            return generateFallbackImage(prompt, style);
        }

        // Generar imagen usando Picsum con filtros
        async function generatePicsumImage(prompt, style) {
            const filters = {
                'manga': '?grayscale&blur=1',
                'comic': '?random=1',
                'watercolor': '?blur=2',
                'noir': '?grayscale'
            };

            const imageId = Math.floor(Math.random() * 1000) + 1;
            const filter = filters[style] || '';

            return `https://picsum.photos/400/300${filter}&random=${imageId}`;
        }

        // Generar placeholder temático
        function generateThemedPlaceholder(prompt, style) {
            const styleColors = {
                'manga': { bg: '2d3748', text: 'ffffff' },
                'comic': { bg: '3182ce', text: 'ffffff' },
                'watercolor': { bg: '38a169', text: 'ffffff' },
                'noir': { bg: '1a202c', text: 'ffffff' }
            };

            const colors = styleColors[style] || styleColors['manga'];
            const text = encodeURIComponent(prompt.substring(0, 20) + '...');

            return `https://via.placeholder.com/400x300/${colors.bg}/${colors.text}?text=${text}`;
        }

        // Imagen de fallback
        function generateFallbackImage(prompt, style) {
            const gradients = {
                'manga': 'linear-gradient(45deg, %23667eea 0%, %23764ba2 100%)',
                'comic': 'linear-gradient(45deg, %23f093fb 0%, %23f5576c 100%)',
                'watercolor': 'linear-gradient(45deg, %2343e97b 0%, %2338f9d7 100%)',
                'noir': 'linear-gradient(45deg, %23434343 0%, %23000000 100%)'
            };

            const gradient = gradients[style] || gradients['manga'];
            return `data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="400" height="300"><defs><linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23667eea;stop-opacity:1" /><stop offset="100%" style="stop-color:%23764ba2;stop-opacity:1" /></linearGradient></defs><rect width="400" height="300" fill="url(%23grad)"/><text x="200" y="150" font-family="Arial" font-size="16" fill="white" text-anchor="middle" dominant-baseline="middle">${prompt.substring(0, 30)}</text></svg>`;
        }

        async function generateComic() {
            if (!appState.dreamAnalyzed || !appState.currentDream) {
                alert('Primero debes analizar un sueño');
                return;
            }

            const selectorEl = document.getElementById('style-selector');
            const generationEl = document.getElementById('comic-generation');
            const comicEl = document.getElementById('generated-comic');
            const progressBar = document.getElementById('comic-progress-bar');
            const progressText = document.getElementById('comic-progress-text');
            const styleNameEl = document.getElementById('selected-style-name');

            selectorEl.classList.add('hidden');
            generationEl.classList.remove('hidden');

            const styleNames = {
                manga: 'Manga',
                comic: 'Cómic',
                watercolor: 'Acuarela',
                noir: 'Noir'
            };

            // Generar paneles basados en el sueño
            const comicData = generateComicPanels(appState.currentDream);

            let progress = 0;
            const interval = setInterval(async () => {
                progress += Math.random() * 15 + 5;
                if (progress > 100) progress = 100;

                progressBar.style.width = progress + '%';
                progressText.textContent = Math.round(progress);

                if (progress >= 100) {
                    clearInterval(interval);

                    // Generar imágenes para los paneles
                    const panelsWithImages = await generatePanelImages(comicData.panels, appState.selectedStyle);

                    setTimeout(() => {
                        generationEl.classList.add('hidden');
                        updateComicDisplay(comicData.title, panelsWithImages);
                        comicEl.classList.remove('hidden');
                        styleNameEl.textContent = styleNames[appState.selectedStyle];
                    }, 500);
                }
            }, 300);
        }

        // Actualizar la visualización del cómic
        function updateComicDisplay(title, panels) {
            // Actualizar título
            document.querySelector('#generated-comic h3').textContent = title;

            // Actualizar paneles
            const panelsContainer = document.querySelector('#generated-comic .grid');
            panelsContainer.innerHTML = panels.map(panel => `
                <div class="comic-panel rounded-lg shadow-lg p-4 ${panel.isScientificData ? 'border-2 border-blue-500' : ''}">
                    ${panel.isScientificData ? `
                        <div class="bg-blue-600 text-white p-2 rounded-lg mb-2 text-xs font-bold">
                            🧠 DATOS EEG CIENTÍFICOS REALES - NO INVENTADOS
                        </div>
                    ` : ''}

                    ${panel.realData ? `
                        <div class="bg-blue-100 p-2 rounded-lg mb-2 text-xs">
                            <strong>Datos Reales:</strong> ${panel.realData}
                        </div>
                    ` : ''}

                    <div class="bg-gray-200 h-48 rounded-lg mb-4 overflow-hidden">
                        <img src="${panel.imageUrl}" alt="${panel.description}"
                             class="w-full h-full object-cover"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <div class="w-full h-full flex items-center justify-center text-center text-gray-600" style="display: none;">
                            <div>
                                ${panel.isScientificData ? `
                                    <svg class="w-12 h-12 mx-auto mb-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    <p class="text-sm px-2 font-bold text-blue-800">DATOS EEG REALES</p>
                                ` : `
                                    <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                `}
                                <p class="text-sm px-2">${panel.description}</p>
                            </div>
                        </div>
                    </div>

                    ${panel.isUserContent ? `
                        <div class="bg-green-100 p-2 rounded-lg mb-2 text-xs">
                            <strong>Tu Sueño:</strong> ${panel.originalText.substring(0, 60)}${panel.originalText.length > 60 ? '...' : ''}
                        </div>
                    ` : ''}

                    <div class="bg-yellow-100 p-3 rounded-lg border-2 border-yellow-400">
                        <p class="text-gray-800 font-medium italic">${panel.dialogue}</p>
                    </div>
                </div>
            `).join('');

            // Guardar datos del cómic actual
            appState.currentComic = { title, panels, style: appState.selectedStyle };
        }

        function resetComicGenerator() {
            document.getElementById('style-selector').classList.remove('hidden');
            document.getElementById('comic-generation').classList.add('hidden');
            document.getElementById('generated-comic').classList.add('hidden');
        }

        function saveComic() {
            if (!appState.currentComic || !appState.currentDream) {
                alert('No hay cómic para guardar');
                return;
            }

            const comic = {
                id: Date.now(),
                title: appState.currentComic.title,
                style: appState.selectedStyle,
                createdAt: new Date().toISOString(),
                panels: appState.currentComic.panels.length,
                dreamData: appState.currentDream,
                comicData: appState.currentComic,
                preview: appState.currentComic.panels[0]?.imageUrl || null
            };

            appState.savedComics.push(comic);
            localStorage.setItem('dreamComics', JSON.stringify(appState.savedComics));

            alert('¡Cómic guardado en la galería!');
        }

        // Galería
        function loadGallery() {
            const emptyEl = document.getElementById('empty-gallery');
            const contentEl = document.getElementById('gallery-content');

            if (appState.savedComics.length === 0) {
                emptyEl.classList.remove('hidden');
                contentEl.classList.add('hidden');
            } else {
                emptyEl.classList.add('hidden');
                contentEl.classList.remove('hidden');
                renderGallery();
            }
        }

        function renderGallery(filter = 'all') {
            const gridEl = document.getElementById('gallery-grid');
            const filteredComics = filter === 'all'
                ? appState.savedComics
                : appState.savedComics.filter(comic => comic.style === filter);

            const styleNames = {
                manga: 'Manga',
                comic: 'Cómic',
                watercolor: 'Acuarela',
                noir: 'Noir'
            };

            gridEl.innerHTML = filteredComics.map(comic => `
                <div class="bg-white/5 rounded-lg p-6 hover:bg-white/10 transition-all cursor-pointer group" onclick="viewComic(${comic.id})">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                            <h3 class="text-lg font-bold text-white mb-1">${comic.title}</h3>
                            <div class="flex items-center gap-2 text-sm text-gray-300">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                                </svg>
                                ${styleNames[comic.style]}
                            </div>
                        </div>
                        <button onclick="event.stopPropagation(); deleteComic(${comic.id})" class="text-red-400 hover:text-red-300 opacity-0 group-hover:opacity-100 transition-opacity">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>

                    <div class="bg-gray-200 h-32 rounded-lg mb-4 overflow-hidden">
                        ${comic.preview ? `
                            <img src="${comic.preview}" alt="Vista previa" class="w-full h-full object-cover"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="w-full h-full flex items-center justify-center text-center text-gray-600" style="display: none;">
                                <div>
                                    <svg class="w-8 h-8 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                    <p class="text-xs">Vista previa del cómic</p>
                                </div>
                            </div>
                        ` : `
                            <div class="w-full h-full flex items-center justify-center text-center text-gray-600">
                                <div>
                                    <svg class="w-8 h-8 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                    <p class="text-xs">Vista previa del cómic</p>
                                </div>
                            </div>
                        `}
                    </div>

                    <div class="space-y-2 text-sm">
                        <div class="flex items-center justify-between text-gray-300">
                            <span>${comic.panels} paneles</span>
                            <span class="flex items-center gap-1">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                ${new Date(comic.createdAt).toLocaleDateString()}
                            </span>
                        </div>

                        ${comic.dreamData ? `
                            <div class="flex flex-wrap gap-1">
                                ${comic.dreamData.emotions.slice(0, 2).map(emotion =>
                                    `<span class="bg-purple-600/30 text-purple-200 px-2 py-1 rounded text-xs">${emotion}</span>`
                                ).join('')}
                            </div>
                            <p class="text-xs text-gray-400 line-clamp-2">${comic.dreamData.narrative.substring(0, 80)}...</p>
                        ` : ''}
                    </div>
                </div>
            `).join('');
        }

        function filterGallery(filter) {
            // Actualizar botones de filtro
            document.querySelectorAll('.filter-btn').forEach(btn => {
                if (btn.dataset.filter === filter) {
                    btn.className = 'filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-all bg-green-600 text-white';
                } else {
                    btn.className = 'filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-all bg-white/10 text-gray-300 hover:bg-white/20';
                }
            });

            renderGallery(filter);
        }

        // Ver cómic completo
        function viewComic(id) {
            const comic = appState.savedComics.find(c => c.id === id);
            if (!comic || !comic.comicData) {
                alert('No se pudo cargar el cómic');
                return;
            }

            // Crear modal para mostrar el cómic completo
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4';
            modal.onclick = (e) => {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            };

            modal.innerHTML = `
                <div class="bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 rounded-xl p-6 max-w-6xl max-h-[90vh] overflow-y-auto">
                    <div class="flex justify-between items-center mb-6">
                        <div>
                            <h2 class="text-2xl font-bold text-white">${comic.title}</h2>
                            <p class="text-purple-200">Estilo: ${comic.style} • ${new Date(comic.createdAt).toLocaleDateString()}</p>
                        </div>
                        <button onclick="document.body.removeChild(this.closest('.fixed'))" class="text-white hover:text-red-300">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    ${comic.dreamData ? `
                        <div class="bg-white/10 rounded-lg p-4 mb-6">
                            <h3 class="text-lg font-bold text-white mb-2">Datos del Sueño Original</h3>
                            <div class="grid md:grid-cols-3 gap-4 text-sm">
                                <div><span class="text-gray-400">Duración:</span> <span class="text-white">${comic.dreamData.duration}</span></div>
                                <div><span class="text-gray-400">Fases REM:</span> <span class="text-white">${comic.dreamData.remPhases}</span></div>
                                <div><span class="text-gray-400">Intensidad:</span> <span class="text-white">${comic.dreamData.intensity}/10</span></div>
                            </div>
                            <div class="mt-3">
                                <span class="text-gray-400">Emociones:</span>
                                <div class="flex flex-wrap gap-2 mt-1">
                                    ${comic.dreamData.emotions.map(emotion =>
                                        `<span class="bg-pink-600/30 text-pink-200 px-2 py-1 rounded text-xs">${emotion}</span>`
                                    ).join('')}
                                </div>
                            </div>
                            <div class="mt-3">
                                <span class="text-gray-400">Narrativa:</span>
                                <p class="text-white text-sm mt-1">${comic.dreamData.narrative}</p>
                            </div>
                        </div>
                    ` : ''}

                    <div class="grid md:grid-cols-2 gap-4">
                        ${comic.comicData.panels.map(panel => `
                            <div class="comic-panel rounded-lg shadow-lg p-4">
                                <div class="bg-gray-200 h-48 rounded-lg mb-4 overflow-hidden">
                                    <img src="${panel.imageUrl}" alt="${panel.description}"
                                         class="w-full h-full object-cover"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                    <div class="w-full h-full flex items-center justify-center text-center text-gray-600" style="display: none;">
                                        <div>
                                            <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                            </svg>
                                            <p class="text-sm">${panel.description}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-yellow-100 p-3 rounded-lg border-2 border-yellow-400">
                                    <p class="text-gray-800 font-medium italic">${panel.dialogue}</p>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        function deleteComic(id) {
            if (confirm('¿Estás seguro de que quieres eliminar este cómic?')) {
                appState.savedComics = appState.savedComics.filter(comic => comic.id !== id);
                localStorage.setItem('dreamComics', JSON.stringify(appState.savedComics));
                loadGallery();
            }
        }

        // Inicializar aplicación
        document.addEventListener('DOMContentLoaded', function() {
            showStep(0);

            // Cargar cómics guardados
            appState.savedComics = JSON.parse(localStorage.getItem('dreamComics') || '[]');

            console.log('🌙 Somnígrafo iniciado - El traductor de sueños en historietas');
            console.log('✨ Funcionalidades mejoradas:');
            console.log('  • Generación de sueños únicos y realistas');
            console.log('  • Imágenes reales para los paneles de cómics');
            console.log('  • Narrativas dinámicas basadas en elementos oníricos');
            console.log('  • Galería mejorada con vista previa de imágenes');
        });
    </script>
</body>
</html>