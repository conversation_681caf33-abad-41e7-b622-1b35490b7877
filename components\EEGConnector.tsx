'use client'

import { useState, useEffect } from 'react'
import { Brain, Wifi, WifiOff, Activity, Zap } from 'lucide-react'

interface EEGConnectorProps {
  onConnect: (connected: boolean) => void
  isConnected: boolean
}

export default function EEGConnector({ onConnect, isConnected }: EEGConnectorProps) {
  const [isConnecting, setIsConnecting] = useState(false)
  const [signalStrength, setSignalStrength] = useState(0)
  const [eegData, setEegData] = useState<number[]>([])

  // Simular datos EEG
  useEffect(() => {
    if (isConnected) {
      const interval = setInterval(() => {
        const newData = Array.from({ length: 50 }, (_, i) =>
          Math.sin(i * 0.1 + Date.now() * 0.001) * 50 +
          Math.random() * 20 - 10
        )
        setEegData(newData)
        setSignalStrength(Math.random() * 100)
      }, 100)

      return () => clearInterval(interval)
    }
  }, [isConnected])

  const handleConnect = async () => {
    setIsConnecting(true)

    // Simular proceso de conexión
    await new Promise(resolve => setTimeout(resolve, 2000))

    onConnect(!isConnected)
    setIsConnecting(false)
  }

  return (
    <div className="dream-card p-8 max-w-4xl mx-auto">
      <div className="text-center mb-8">
        <Brain className="w-16 h-16 text-purple-300 mx-auto mb-4 animate-pulse" />
        <h2 className="text-3xl font-bold text-white mb-2">Conexión EEG</h2>
        <p className="text-purple-200">
          Conecta tu banda cerebral para comenzar a capturar las ondas de tus sueños
        </p>
      </div>

      {/* Estado de Conexión */}
      <div className="grid md:grid-cols-2 gap-8">
        <div className="space-y-6">
          <div className="bg-white/5 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <span className="text-white font-medium">Estado del Dispositivo</span>
              {isConnected ? (
                <Wifi className="w-6 h-6 text-green-400" />
              ) : (
                <WifiOff className="w-6 h-6 text-red-400" />
              )}
            </div>

            <div className={`text-lg font-bold ${
              isConnected ? 'text-green-400' : 'text-red-400'
            }`}>
              {isConnecting ? 'Conectando...' : isConnected ? 'Conectado' : 'Desconectado'}
            </div>
          </div>

          {isConnected && (
            <div className="bg-white/5 rounded-lg p-6">
              <div className="flex items-center gap-2 mb-4">
                <Activity className="w-5 h-5 text-blue-400" />
                <span className="text-white font-medium">Calidad de Señal</span>
              </div>

              <div className="w-full bg-gray-700 rounded-full h-3 mb-2">
                <div
                  className="bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${signalStrength}%` }}
                />
              </div>

              <div className="text-sm text-purple-200">
                {signalStrength > 80 ? 'Excelente' :
                 signalStrength > 60 ? 'Buena' :
                 signalStrength > 40 ? 'Regular' : 'Débil'}
              </div>
            </div>
          )}

          <button
            onClick={handleConnect}
            disabled={isConnecting}
            className={`w-full py-4 px-6 rounded-lg font-bold text-lg transition-all ${
              isConnecting
                ? 'bg-gray-600 text-gray-300 cursor-not-allowed'
                : isConnected
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-purple-600 hover:bg-purple-700 text-white'
            }`}
          >
            {isConnecting ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Conectando...
              </div>
            ) : isConnected ? (
              'Desconectar'
            ) : (
              'Conectar Banda EEG'
            )}
          </button>
        </div>

        {/* Visualización de Ondas Cerebrales */}
        <div className="bg-white/5 rounded-lg p-6">
          <div className="flex items-center gap-2 mb-4">
            <Zap className="w-5 h-5 text-yellow-400" />
            <span className="text-white font-medium">Ondas Cerebrales en Tiempo Real</span>
          </div>

          <div className="h-48 bg-black/30 rounded-lg p-4 overflow-hidden">
            {isConnected ? (
              <svg className="w-full h-full">
                <polyline
                  fill="none"
                  stroke="#8B5CF6"
                  strokeWidth="2"
                  points={eegData.map((value, index) =>
                    `${(index / eegData.length) * 300},${100 + value * 0.8}`
                  ).join(' ')}
                  className="brain-wave"
                />
              </svg>
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                <div className="text-center">
                  <Brain className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>Conecta tu dispositivo EEG para ver las ondas cerebrales</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Instrucciones */}
      <div className="mt-8 bg-blue-900/20 border border-blue-500/30 rounded-lg p-6">
        <h3 className="text-lg font-bold text-blue-300 mb-3">Instrucciones de Uso</h3>
        <ul className="space-y-2 text-blue-200">
          <li>• Coloca la banda EEG en tu frente antes de dormir</li>
          <li>• Asegúrate de que los sensores estén en contacto con la piel</li>
          <li>• La aplicación capturará automáticamente las ondas durante el sueño REM</li>
          <li>• Al despertar, podrás generar tu historieta personalizada</li>
        </ul>
      </div>
    </div>
  )
}
