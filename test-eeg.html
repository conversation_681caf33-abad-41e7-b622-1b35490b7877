<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test EEG Simple</title>
    <style>
        body {
            background: #1a1a2e;
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
            text-align: center;
        }
        
        .eeg-container {
            background: #16213e;
            border: 3px solid #00ff00;
            border-radius: 10px;
            padding: 20px;
            margin: 20px auto;
            max-width: 400px;
        }
        
        .eeg-monitor {
            background: #000000;
            border: 2px solid #00ff00;
            border-radius: 5px;
            width: 100%;
            height: 100px;
            position: relative;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .eeg-grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(to right, #00ff0033 1px, transparent 1px),
                linear-gradient(to bottom, #00ff0033 1px, transparent 1px);
            background-size: 20px 15px;
        }
        
        .eeg-line {
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            background: #ffffff;
            transform: translateY(-50%);
        }
        
        .eeg-wave {
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            background: #ff00ff;
            transform: translateY(-50%);
            animation: wave 2s infinite linear;
        }
        
        @keyframes wave {
            0% { transform: translateY(-50%) translateX(-100%); }
            100% { transform: translateY(-50%) translateX(100%); }
        }
        
        .status {
            color: #00ff00;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .btn {
            background: #0066cc;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
        }
        
        .btn:hover {
            background: #0088ff;
        }
        
        .btn-red {
            background: #cc0000;
        }
        
        .btn-red:hover {
            background: #ff0000;
        }
    </style>
</head>
<body>
    <h1>🧠 Test EEG Monitor</h1>
    
    <div class="eeg-container">
        <h2>Monitor EEG</h2>
        
        <div class="eeg-monitor">
            <div class="eeg-grid"></div>
            <div class="eeg-line"></div>
            <div class="eeg-wave" id="wave"></div>
            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #00ff00; font-size: 12px; z-index: 10;">
                EEG ACTIVO
            </div>
        </div>
        
        <div class="status" id="status">Listo para capturar ondas cerebrales</div>
        <div id="timer" style="color: #ffff00;">Tiempo: 0:00</div>
        <div id="data-count" style="color: #ffff00;">Datos: 0 puntos</div>
    </div>
    
    <button class="btn" onclick="startCapture()">Iniciar Captura EEG</button>
    <button class="btn btn-red" onclick="stopCapture()" id="stop-btn" style="display: none;">Detener y Analizar</button>
    
    <div id="results" style="display: none; margin-top: 20px;">
        <h3>Resultados del Análisis EEG</h3>
        <div id="eeg-data" style="background: #333; padding: 15px; border-radius: 5px; margin: 10px 0;"></div>
    </div>

    <script>
        let capturing = false;
        let startTime = null;
        let dataPoints = 0;
        let timer = null;
        
        function startCapture() {
            capturing = true;
            startTime = Date.now();
            dataPoints = 0;
            
            document.getElementById('status').textContent = 'Capturando ondas cerebrales...';
            document.querySelector('.btn').style.display = 'none';
            document.getElementById('stop-btn').style.display = 'inline-block';
            
            // Iniciar timer
            timer = setInterval(updateTimer, 1000);
            
            // Simular captura de datos
            setInterval(() => {
                if (capturing) {
                    dataPoints++;
                    document.getElementById('data-count').textContent = `Datos: ${dataPoints} puntos`;
                }
            }, 100);
            
            console.log('Captura EEG iniciada');
        }
        
        function stopCapture() {
            capturing = false;
            clearInterval(timer);
            
            document.getElementById('status').textContent = 'Analizando datos...';
            document.getElementById('stop-btn').style.display = 'none';
            
            setTimeout(() => {
                showResults();
            }, 2000);
        }
        
        function updateTimer() {
            if (!startTime) return;
            
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            
            document.getElementById('timer').textContent = 
                `Tiempo: ${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
        
        function showResults() {
            const duration = Math.floor((Date.now() - startTime) / 1000);
            
            document.getElementById('eeg-data').innerHTML = `
                <h4>Datos EEG Capturados:</h4>
                <p><strong>Duración:</strong> ${duration} segundos</p>
                <p><strong>Puntos de datos:</strong> ${dataPoints}</p>
                <p><strong>Frecuencia promedio:</strong> ${(Math.random() * 10 + 5).toFixed(1)} Hz</p>
                <p><strong>Amplitud promedio:</strong> ${(Math.random() * 50 + 25).toFixed(0)} μV</p>
                <p><strong>Tipo de onda dominante:</strong> ${['Delta', 'Theta', 'Alfa', 'Beta'][Math.floor(Math.random() * 4)]}</p>
            `;
            
            document.getElementById('results').style.display = 'block';
            document.getElementById('status').textContent = 'Análisis completado';
            
            // Mostrar botón para reiniciar
            setTimeout(() => {
                document.querySelector('.btn').style.display = 'inline-block';
                document.querySelector('.btn').textContent = 'Nueva Captura';
            }, 1000);
        }
    </script>
</body>
</html>
