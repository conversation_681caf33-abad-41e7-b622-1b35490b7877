@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
  }
}

@layer components {
  .dream-card {
    @apply bg-white/10 backdrop-blur-lg rounded-xl border border-white/20 shadow-xl;
  }
  
  .eeg-wave {
    animation: wave 2s ease-in-out infinite;
  }
  
  .comic-panel {
    @apply bg-white rounded-lg shadow-lg border-2 border-gray-800 p-4;
    background: radial-gradient(circle at 50% 50%, #fff 0%, #f8f9fa 100%);
  }
}

@keyframes wave {
  0%, 100% { transform: scaleY(1); }
  50% { transform: scaleY(1.5); }
}

.brain-wave {
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
  animation: draw 3s ease-in-out infinite;
}

@keyframes draw {
  0% { stroke-dashoffset: 1000; }
  50% { stroke-dashoffset: 0; }
  100% { stroke-dashoffset: -1000; }
}
