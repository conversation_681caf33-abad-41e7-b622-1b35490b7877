'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, <PERSON>, <PERSON>rk<PERSON>, BarChart3, Eye, <PERSON>, Zap } from 'lucide-react'

interface DreamAnalyzerProps {
  isConnected: boolean
  onDreamAnalyzed: (data: any) => void
}

export default function DreamAnalyzer({ isConnected, onDreamAnalyzed }: DreamAnalyzerProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysisProgress, setAnalysisProgress] = useState(0)
  const [dreamData, setDreamData] = useState<any>(null)
  const [sleepPhases, setSleepPhases] = useState<any[]>([])

  // Simular análisis de sueño
  const analyzeDream = async () => {
    if (!isConnected) return

    setIsAnalyzing(true)
    setAnalysisProgress(0)

    // Simular progreso de análisis
    for (let i = 0; i <= 100; i += 10) {
      await new Promise(resolve => setTimeout(resolve, 200))
      setAnalysisProgress(i)
    }

    // Generar datos de sueño simulados
    const mockDreamData = {
      duration: '7h 23m',
      remPhases: 4,
      deepSleepPercentage: 23,
      emotions: ['curiosidad', 'aventura', 'nostalgia'],
      themes: ['viaje', 'familia', 'naturaleza'],
      characters: ['persona misteriosa', 'animal guía', 'figura familiar'],
      settings: ['bosque encantado', 'casa de la infancia', 'ciudad flotante'],
      narrative: 'Un viaje épico a través de un bosque mágico donde encuentras a un guía animal que te lleva a tu casa de la infancia, pero esta vez está flotando en las nubes. Una figura familiar te espera con un mensaje importante sobre tu futuro.',
      intensity: 8.5,
      lucidity: 3.2
    }

    const mockSleepPhases = [
      { phase: 'Ligero', duration: 45, color: '#93C5FD' },
      { phase: 'Profundo', duration: 90, color: '#3B82F6' },
      { phase: 'REM', duration: 25, color: '#8B5CF6' },
      { phase: 'Profundo', duration: 85, color: '#3B82F6' },
      { phase: 'REM', duration: 30, color: '#8B5CF6' },
      { phase: 'Ligero', duration: 40, color: '#93C5FD' },
      { phase: 'REM', duration: 35, color: '#8B5CF6' },
      { phase: 'Profundo', duration: 70, color: '#3B82F6' },
      { phase: 'REM', duration: 40, color: '#8B5CF6' },
      { phase: 'Ligero', duration: 20, color: '#93C5FD' }
    ]

    setDreamData(mockDreamData)
    setSleepPhases(mockSleepPhases)
    onDreamAnalyzed(mockDreamData)
    setIsAnalyzing(false)
  }

  return (
    <div className="dream-card p-8 max-w-6xl mx-auto">
      <div className="text-center mb-8">
        <Moon className="w-16 h-16 text-blue-300 mx-auto mb-4 animate-pulse" />
        <h2 className="text-3xl font-bold text-white mb-2">Análisis de Sueños</h2>
        <p className="text-blue-200">
          Procesando las ondas cerebrales capturadas durante tu sueño
        </p>
      </div>

      {!isConnected ? (
        <div className="text-center py-12">
          <Brain className="w-24 h-24 text-gray-500 mx-auto mb-4 opacity-50" />
          <p className="text-gray-400 text-lg">
            Conecta tu dispositivo EEG primero para analizar tus sueños
          </p>
        </div>
      ) : !dreamData ? (
        <div className="text-center">
          <button
            onClick={analyzeDream}
            disabled={isAnalyzing}
            className={`py-4 px-8 rounded-lg font-bold text-lg transition-all ${
              isAnalyzing 
                ? 'bg-gray-600 text-gray-300 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
          >
            {isAnalyzing ? (
              <div className="flex items-center gap-2">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Analizando Sueño...
              </div>
            ) : (
              'Iniciar Análisis de Sueño'
            )}
          </button>

          {isAnalyzing && (
            <div className="mt-8 max-w-md mx-auto">
              <div className="bg-white/10 rounded-full h-3 mb-4">
                <div 
                  className="bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${analysisProgress}%` }}
                />
              </div>
              <p className="text-blue-200">Procesando ondas cerebrales... {analysisProgress}%</p>
            </div>
          )}
        </div>
      ) : (
        <div className="space-y-8">
          {/* Resumen del Sueño */}
          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-white/5 rounded-lg p-6 text-center">
              <Moon className="w-8 h-8 text-blue-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">{dreamData.duration}</div>
              <div className="text-blue-200">Duración del Sueño</div>
            </div>
            
            <div className="bg-white/5 rounded-lg p-6 text-center">
              <Zap className="w-8 h-8 text-purple-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">{dreamData.remPhases}</div>
              <div className="text-purple-200">Fases REM</div>
            </div>
            
            <div className="bg-white/5 rounded-lg p-6 text-center">
              <BarChart3 className="w-8 h-8 text-green-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">{dreamData.deepSleepPercentage}%</div>
              <div className="text-green-200">Sueño Profundo</div>
            </div>
          </div>

          {/* Fases del Sueño */}
          <div className="bg-white/5 rounded-lg p-6">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
              <BarChart3 className="w-6 h-6" />
              Fases del Sueño
            </h3>
            <div className="flex h-12 rounded-lg overflow-hidden">
              {sleepPhases.map((phase, index) => (
                <div
                  key={index}
                  className="flex items-center justify-center text-white text-xs font-medium"
                  style={{
                    backgroundColor: phase.color,
                    width: `${(phase.duration / 480) * 100}%`
                  }}
                  title={`${phase.phase}: ${phase.duration}min`}
                >
                  {phase.duration > 30 && phase.phase}
                </div>
              ))}
            </div>
            <div className="flex justify-between mt-2 text-sm text-gray-300">
              <span>0h</span>
              <span>2h</span>
              <span>4h</span>
              <span>6h</span>
              <span>8h</span>
            </div>
          </div>

          {/* Análisis del Contenido */}
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-white/5 rounded-lg p-6">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                <Heart className="w-5 h-5 text-pink-400" />
                Emociones Detectadas
              </h3>
              <div className="flex flex-wrap gap-2">
                {dreamData.emotions.map((emotion: string, index: number) => (
                  <span key={index} className="bg-pink-600/30 text-pink-200 px-3 py-1 rounded-full text-sm">
                    {emotion}
                  </span>
                ))}
              </div>
            </div>

            <div className="bg-white/5 rounded-lg p-6">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                <Sparkles className="w-5 h-5 text-yellow-400" />
                Temas Principales
              </h3>
              <div className="flex flex-wrap gap-2">
                {dreamData.themes.map((theme: string, index: number) => (
                  <span key={index} className="bg-yellow-600/30 text-yellow-200 px-3 py-1 rounded-full text-sm">
                    {theme}
                  </span>
                ))}
              </div>
            </div>
          </div>

          {/* Narrativa del Sueño */}
          <div className="bg-white/5 rounded-lg p-6">
            <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5 text-blue-400" />
              Narrativa Interpretada
            </h3>
            <p className="text-blue-100 leading-relaxed">
              {dreamData.narrative}
            </p>
          </div>

          {/* Métricas Avanzadas */}
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-white/5 rounded-lg p-6">
              <h4 className="text-white font-medium mb-2">Intensidad del Sueño</h4>
              <div className="w-full bg-gray-700 rounded-full h-3">
                <div 
                  className="bg-gradient-to-r from-green-500 to-red-500 h-3 rounded-full"
                  style={{ width: `${dreamData.intensity * 10}%` }}
                />
              </div>
              <div className="text-sm text-gray-300 mt-1">{dreamData.intensity}/10</div>
            </div>

            <div className="bg-white/5 rounded-lg p-6">
              <h4 className="text-white font-medium mb-2">Nivel de Lucidez</h4>
              <div className="w-full bg-gray-700 rounded-full h-3">
                <div 
                  className="bg-gradient-to-r from-purple-500 to-blue-500 h-3 rounded-full"
                  style={{ width: `${dreamData.lucidity * 10}%` }}
                />
              </div>
              <div className="text-sm text-gray-300 mt-1">{dreamData.lucidity}/10</div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
